#define IMGUI_DEFINE_MATH_OPERATORS

#include "nav_elements.h"
#include "windows.h"
#include <unordered_map>
#include <algorithm> // For std::min
#include <iostream>
#include <cmath> // For sin, cos, PI, std::abs
#include <cstdlib> // For rand
#include <vector> // For std::vector
#include <string> // For std::string
// Include HotkeySystem.h early to ensure complete type definition
#include "../../Cheat Core/Features/Input/HotkeySystem.h"
#include "PremiumSystem_inline.h"
#include "../ImGui/imgui.h"
#include "../ImGui/imgui_internal.h"
#include "../../Utils.h"
#include "../Fonts/font_defines.h"
#include "../Fonts/custom_icons.h"
#include "../../Cheat Core/Settings/Settings.h"
#include "../../Cheat Core/Features/FontSystem/FontSystem.h"

// Global font system instance
extern FontSystem g_FontSystem;

// Global static maps for expansion state (shared between functions)
static std::map<std::string, bool> g_expandedFeaturesMap;
static std::map<std::string, float> g_expansionAnimationState;

// Forward declaration for helper function


// Inline expansion system functions (defined early to avoid forward declaration issues)
bool nav_elements::IsFeatureExpanded(const std::string& featureName) {
    return g_expandedFeaturesMap[featureName];
}

float nav_elements::GetFeatureExpansionState(const std::string& featureName) {
    return g_expansionAnimationState[featureName];
}

// Additional icon definitions needed for CheckboxComponent
#define ICON_CIRCLE_FILL "\uea80"
#define ICON_SETTINGS_LINE "\uf1de"

// Logging macro (if not already defined)
#ifndef LOG_DEBUG
#define DEBUG_LOG 0
#if DEBUG_LOG
#define LOG_DEBUG(msg, ...) printf("[DEBUG_NAV] " msg "\n", ##__VA_ARGS__)
#else
#define LOG_DEBUG(msg, ...)
#endif
#endif

using namespace ImGui;

void nav_elements::Theme()
{
    ImGuiStyle* style = &ImGui::GetStyle();
    ImVec4* colors = style->Colors;
    style->Colors[ImGuiCol_ChildBg].w = 5.3f;
    colors[ImGuiCol_Text] = ImVec4(255 / 255.f, 255 / 255.f, 255 / 255.f, 255 / 255.f);
    colors[ImGuiCol_TextDisabled] = ImVec4(0.50f, 0.50f, 0.50f, 1.00f);
    colors[ImGuiCol_WindowBg] = ImVec4(18 / 255.f, 14 / 255.f, 11 / 255.f, 255 / 255.f); // Made darker and fully opaque
    colors[ImGuiCol_ChildBg] = ImVec4(15 / 255.f, 12 / 255.f, 10 / 255.f, 0.95f); // Made darker and more opaque
    colors[ImGuiCol_PopupBg] = ImVec4(20 / 255.f, 16 / 255.f, 13 / 255.f, 255 / 255.f); // Made fully opaque
    colors[ImGuiCol_Border] = ImVec4(81 / 255.f, 65 / 255.f, 55 / 255.f, 255 / 255.f); // Swapped
    colors[ImGuiCol_BorderShadow] = ImVec4(0.90f, 0.40f, 0.40f, 0.00f); // Swapped
    colors[ImGuiCol_FrameBg] = ImVec4(41 / 255.f, 34 / 255.f, 33 / 255.f, 180 / 255.f); // Swapped
    colors[ImGuiCol_FrameBgHovered] = ImVec4(37 / 255.f, 31 / 255.f, 30 / 255.f, 200 / 255.f); // Swapped
    colors[ImGuiCol_FrameBgActive] = ImVec4(37 / 255.f, 31 / 255.f, 30 / 255.f, 200 / 255.f); // Swapped
    colors[ImGuiCol_TitleBg] = ImVec4(0.04f, 0.04f, 0.04f, 1.00f);
    colors[ImGuiCol_TitleBgActive] = ImVec4(0.48f, 0.29f, 0.16f, 1.00f); // Swapped
    colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.00f, 0.00f, 0.00f, 0.51f);
    colors[ImGuiCol_MenuBarBg] = ImVec4(0.14f, 0.14f, 0.14f, 1.00f);
    colors[ImGuiCol_ScrollbarBg] = ImVec4(0.0f, 0.0f, 0.0f, 0.00f);
    colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.0f, 0.0f, 0.0f, 0.00f); // Made completely transparent
    colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.0f, 0.0f, 0.0f, 0.00f); // Made completely transparent  
    colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.0f, 0.0f, 0.0f, 0.00f); // Made completely transparent
    colors[ImGuiCol_CheckMark] = ImVec4(0.20f, 0.20f, 0.20f, 1.00f);
    colors[ImGuiCol_SliderGrab] = ImVec4(0.88f, 0.52f, 0.24f, 1.00f); // Swapped
    colors[ImGuiCol_SliderGrabActive] = ImVec4(0.98f, 0.59f, 0.26f, 1.00f); // Swapped
    colors[ImGuiCol_Button] = ImVec4(44 / 255.f, 34 / 255.f, 27 / 255.f, 50 / 255.f); // Swapped
    colors[ImGuiCol_ButtonHovered] = ImVec4(44 / 255.f, 34 / 255.f, 27 / 255.f, 50 / 255.f); // Swapped
    colors[ImGuiCol_ButtonActive] = ImVec4(44 / 255.f, 34 / 255.f, 27 / 255.f, 50 / 255.f); // Swapped
    colors[ImGuiCol_Header] = ImVec4(0.10f, 0.10f, 0.10f, 0.70f);
    colors[ImGuiCol_HeaderHovered] = ImVec4(0.45f, 0.45f, 0.45f, 0.10f);
    colors[ImGuiCol_HeaderActive] = ImVec4(0.45f, 0.45f, 0.45f, 0.10f);
    colors[ImGuiCol_Separator] = colors[ImGuiCol_Border];
    colors[ImGuiCol_SeparatorHovered] = ImVec4(0.75f, 0.40f, 0.10f, 0.78f); // Swapped
    colors[ImGuiCol_SeparatorActive] = ImVec4(0.75f, 0.40f, 0.10f, 1.00f); // Swapped
    colors[ImGuiCol_ResizeGrip] = ImVec4(0.98f, 0.59f, 0.26f, 0.25f); // Swapped
    colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.98f, 0.59f, 0.26f, 0.67f); // Swapped
    colors[ImGuiCol_ResizeGripActive] = ImVec4(0.98f, 0.59f, 0.26f, 0.95f); // Swapped
    colors[ImGuiCol_Tab] = ImLerp(colors[ImGuiCol_Header], colors[ImGuiCol_TitleBgActive], 0.80f);
    colors[ImGuiCol_TabHovered] = colors[ImGuiCol_HeaderHovered];
    colors[ImGuiCol_TabActive] = ImLerp(colors[ImGuiCol_HeaderActive], colors[ImGuiCol_TitleBgActive], 0.60f);
    colors[ImGuiCol_TabUnfocused] = ImLerp(colors[ImGuiCol_Tab], colors[ImGuiCol_TitleBg], 0.80f);
    colors[ImGuiCol_TabUnfocusedActive] = ImLerp(colors[ImGuiCol_TabActive], colors[ImGuiCol_TitleBg], 0.40f);
    colors[ImGuiCol_PlotLines] = ImVec4(0.61f, 0.61f, 0.61f, 1.00f);
    colors[ImGuiCol_PlotLinesHovered] = ImVec4(0.35f, 0.43f, 1.00f, 1.00f); // Swapped
    colors[ImGuiCol_PlotHistogram] = ImVec4(0.00f, 0.70f, 0.90f, 1.00f); // Swapped
    colors[ImGuiCol_PlotHistogramHovered] = ImVec4(0.00f, 0.60f, 1.00f, 1.00f); // Swapped
    colors[ImGuiCol_TextSelectedBg] = ImVec4(124 / 255.f, 54 / 255.f, 90 / 255.f, 80 / 255.f); // Swapped
    colors[ImGuiCol_DragDropTarget] = ImVec4(0.00f, 1.00f, 1.00f, 0.90f); // Swapped
    colors[ImGuiCol_NavHighlight] = ImVec4(0.98f, 0.59f, 0.26f, 1.00f); // Swapped
    colors[ImGuiCol_NavWindowingHighlight] = ImVec4(1.00f, 1.00f, 1.00f, 0.70f);
    colors[ImGuiCol_NavWindowingDimBg] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f); // Disabled modal dimming
    colors[ImGuiCol_ModalWindowDimBg] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f); // Disabled modal dimming
    style->WindowBorderSize = 1;
    style->WindowRounding = 12.f;
    style->FrameRounding = 4.f;
    style->FramePadding = ImVec2(10, 10);
    style->WindowPadding = ImVec2(0, 0);
    style->ItemSpacing = ImVec2(10, 10);
    style->ChildBorderSize = 0;
    style->ChildRounding = 12.f;
    style->ScrollbarRounding = 15.f;
    style->ScrollbarSize = 13.f;
    style->PopupRounding = 4.f;
    style->PopupBorderSize = 1;
    style->GrabMinSize = 0;
}

struct tab_states {
    ImVec4 text_col[2], icon_col;
    ImVec4 frame_col;
    ImVec4 line_col;
    bool is_want = true;
};

struct tab_elements {
    float element_opacity; // Opacity for the background element
    float rect_opacity;    // Opacity for the rectangle
    float text_opacity;    // Opacity for the text
    ImVec4 icon_color;     // Color for the icon (with alpha for opacity)
    float line_opacity;    // Opacity for the left line
    float shadow_opacity;  // Opacity for the shadow (only shown when selected)
};
std::map<ImGuiID, tab_elements> anim_2;

bool nav_elements::Tab(const char* label, const char* icon, int* v, int number) {
    // Check if the current tab is active
    bool is_active = (*v == number);

    // Call the elements::tab function with the icon and label
    if (TabVS2(icon, label, is_active)) {
        // If the tab is pressed, update the selected tab
        *v = number;
        return true;
    }

    return false;
}
bool nav_elements::TabVS2(const char* icon, const char* name, bool boolean)
{
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(name);
    ImGui::PushFont(iconsBig);
    const ImVec2 icon_size = ImGui::CalcTextSize(icon);
    ImGui::PopFont();

    const ImVec2 label_size = ImGui::CalcTextSize(name); // Size of the label
    ImVec2 pos = window->DC.CursorPos;

    const ImRect rect(pos, ImVec2(pos.x + 181, pos.y + 42));
    ImGui::ItemSize(ImVec4(rect.Min.x, rect.Min.y, rect.Max.x, rect.Max.y + 5), style.FramePadding.y);
    if (!ImGui::ItemAdd(rect, id))
        return false;

    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(rect, id, &hovered, &held, NULL);

    if (hovered || boolean) {
        ImGui::SetMouseCursor(ImGuiMouseCursor_Hand); // Set cursor to hand
    }

    auto it_anim = anim_2.find(id);
    if (it_anim == anim_2.end()) {
        anim_2.insert({ id, { 0.0f, 0.0f, 0.0f, ImVec4(1.0f, 1.0f, 1.0f, 0.7f), 0.0f } }); // Initialize icon color and line opacity
        it_anim = anim_2.find(id);
    }

    ImVec2 size({ window->Size.x, 24 });
    if (pressed)
        content_anim = 0.f;

    // Animate line opacity
    it_anim->second.line_opacity = ImLerp(it_anim->second.line_opacity, (boolean || hovered) ? 1.0f : 0.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));


    // Animate element opacity
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity, (boolean ? 0.04f : hovered ? 0.01f : 0.0f), 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.rect_opacity = ImLerp(it_anim->second.rect_opacity, (boolean ? 1.0f : 0.0f), 0.15f * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.text_opacity = ImLerp(it_anim->second.text_opacity, (boolean ? 1.0f : hovered ? 0.5f : 0.3f), 0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Animate icon color
    it_anim->second.icon_color = ImLerp(
        it_anim->second.icon_color,
        boolean || hovered ? gui.main : ImColor(1.0f, 1.0f, 1.0f, 0.7f), // Darker shadow when active or hovered
        GetAnimSpeed()
    );

    // Draw the background rectangle
    window->DrawList->AddRectFilled(rect.Min, rect.Max, ImColor(1.0f, 1.0f, 1.0f, it_anim->second.element_opacity), 3.0f);

    // Draw the border
    ImVec4 border_color = boolean || hovered ? ImVec4(ImColor(255, 255, 255, 10)) : ImVec4(ImColor(255, 255, 255, 10)); // Grey when disabled, main color when hovered/clicked
    window->DrawList->AddRect(rect.Min, rect.Max, ImGui::ColorConvertFloat4ToU32(border_color), 3.0f, 0, 1.0f);


    const ImVec2 label_pos = ImVec2(rect.GetCenter().x - label_size.x / 2, rect.GetCenter().y - label_size.y / 2 - 2); // Middle for label

    float rect_height = rect.GetHeight(); // Should be 42
    float rect_center_y = rect.Min.y + (rect_height * 0.5f);

    // Padding on the X-axis for the icon:
    const float icon_padding = 5.0f;

    // Calculate the position for the icon so it's centered vertically:
    ImVec2 icon_pos = ImVec2(
        rect.Min.x + icon_padding,
        rect_center_y - (icon_size.y * 0.5f) // center the icon text
    );

    // Calculate the center of the icon for AddShadowCircle:
    ImVec2 icon_center = icon_pos + (icon_size * 0.5f);


    // Draw the icon shadow
    window->DrawList->AddShadowCircle(
        icon_center, // Center of the icon
        9.f, // Shadow radius
        ImGui::GetColorU32(it_anim->second.icon_color), // Animated shadow color
        40, // Shadow spread
        ImVec2(0, 0), // Shadow offset
        0, // Shadow flags (0 for default)
        360 // Shadow rounding
    );

    ImGui::PushFont(iconsBig); // Use the desired font for the icon
    window->DrawList->AddText(
        icon_pos,
        ImGui::GetColorU32(it_anim->second.icon_color), // Use the animated icon color
        icon
    );
    ImGui::PopFont();

    // Draw the label
    window->DrawList->AddText(
        label_pos, // Adjust label position as needed
        ImColor(1.0f, 1.0f, 1.0f, it_anim->second.text_opacity), // Use the desired text color
        name
    );

    // Define the rectangle for the line (inside the button on the left)
    const float line_width = 1.0f; // Width of the glowing line
    ImVec2 line_start = { rect.Min.x + 1, rect.Min.y + 10 }; // Inside the button on the left
    ImVec2 line_end = { rect.Min.x + 1 + line_width, rect.Max.y - 10 }; // Full height of the button

    if (boolean || hovered) {
        window->DrawList->AddShadowRect(
            line_start, // Start position of the line
            line_end,   // End position of the line
            gui.main, // Shadow color
            25.f,       // Shadow size (larger shadow)
            ImVec2(0, 0), // Shadow offset
            0,          // Shadow flags (0 for default)
            60.f       // Shadow rounding
        );
    }

    // Add the glowing line with animated opacity
    ImColor glow_color = ImColor(accent_color[2], accent_color[1], accent_color[0], it_anim->second.line_opacity); // Glow color with opacity
    window->DrawList->AddRectFilled(
        line_start, // Start position of the line
        line_end,   // End position of the line
        glow_color, // Glow color with opacity
        360.f,      // Rounding radius (360.f for fully rounded ends)
        ImDrawFlags_RoundCornersTop | ImDrawFlags_RoundCornersBottom // Round top and bottom corners
    );


    // Define the rectangle for the line (inside the button on the left)
    const float line_widths = 1.0f; // Width of the glowing line
    ImVec2 line_starts = { rect.Min.x + 1, rect.Min.y + 10 }; // Inside the button on the left
    ImVec2 line_ends = { rect.Min.x + 1 + line_widths, rect.Max.y - 10 }; // Full height of the button

    // Add shadow to the line (only when hovered or selected)
    if (boolean || hovered) {
        window->DrawList->AddShadowRect(
            line_starts, // Start position of the line
            line_ends,   // End position of the line
            ImGui::ColorConvertFloat4ToU32(glow_color), // Shadow color
            25.f,       // Shadow size (larger shadow)
            ImVec2(0, 0), // Shadow offset
            0,          // Shadow flags (0 for default)
            60.f       // Shadow rounding
        );
    }

    // Add the glowing line with animated opacity
    window->DrawList->AddRectFilled(
        line_starts, // Start position of the line
        line_ends,   // End position of the line
        glow_color, // Glow color with opacity
        360.f,      // Rounding radius (360.f for fully rounded ends)
        ImDrawFlags_RoundCornersTop | ImDrawFlags_RoundCornersBottom // Round top and bottom corners
    );

    return pressed;
}////////////

struct check_state {
    ImVec4 check_color, check_rect_color, check_color_circle, check_color_shadow, icon_color, rect_color, rect_shadow_color, background_color, background_rect_color;
    float circle_alpha;
    float element_opacity; // Opacity for the background element
    ImVec2 circle_offset;
    ImVec2 picker_offset{ 0, 200.f };
    
    // Additional members for popup functionality
    float alpha = 0.f;
    bool want_close = false;
};

// Premium animation state for CheckboxComponent
struct premium_state {
    float premiumBadgeOpacity = 0.0f;
    float premiumBadgeScale = 1.0f;
    float upgradeButtonOpacity = 0.0f;
    float upgradeButtonScale = 1.0f;
    float lockOverlayOpacity = 0.0f;
    float premiumGlow = 0.0f;
    float premiumPulse = 0.0f;
    float time = 0.0f;
    bool isLocked = false;
    // New star animation properties
    float starAnimationTime = 0.0f;
    struct StarData {
        ImVec2 position;
        float phase;
        float size;
        float opacity;
    };
    std::vector<StarData> stars;
};

// Constants for CheckboxComponent to avoid magic numbers


// Slider components now use check_state for consistent styling with CheckboxComponent
bool nav_elements::SliderScalar(const char* label,
    ImGuiDataType data_type,
    void* p_data,
    const void* p_min,
    const void* p_max,
    const char* format,
    ImGuiSliderFlags flags,
    const char* description, const char* icon) {
    // --- Input validation to prevent crashes ---
    if (!label || !p_data || !p_min || !p_max || !description) {
        return false;
    }

    // --- Early return check ---
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems)
        return false;

    // --- Setup and ID generation ---
    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(label);

    // --- Layout calculations - EXACTLY matching CheckboxComponent ---
    const ImVec2 pos = window->DC.CursorPos;
    const float content_width = GetContentRegionMax().x - style.GrabMinSize - g.Style.WindowPadding.x;
    const float component_height = 60.f;

    const ImVec2 total_size(content_width, component_height);
    const ImRect total_bb(pos, ImVec2(pos.x + total_size.x, pos.y + total_size.y));
    ItemSize(total_bb, 0.f);

    if (!ItemAdd(total_bb, id))
        return false;

    // --- Slider dimensions and positioning - same as checkbox positioning ---
    const ImVec2 slider_size = ImVec2(60, 26);
    const float slider_spacing = (total_bb.GetHeight() - slider_size.y) / 2;
    const ImRect slider_bb(
        ImVec2(total_bb.Max.x - slider_spacing - slider_size.x, total_bb.Max.y - slider_spacing - slider_size.y),
        ImVec2(total_bb.Max.x - slider_spacing, total_bb.Max.y - slider_spacing)
    );

    // --- Input handling ---
    bool hovered, held;
    bool pressed = ButtonBehavior(total_bb, id, &hovered, &held);

    // --- Text positioning - EXACTLY matching CheckboxComponent ---
    const ImVec2 description_size = CalcTextSize(description, NULL, true);
    const ImVec2 label_size = CalcTextSize(label, NULL, true);
    const ImVec2 label_pos = ImVec2(
        total_bb.Min.x + 60.f + (total_bb.GetSize().y - (label_size.y + description_size.y)) / 2,
        total_bb.GetCenter().y - (label_size.y + description_size.y) / 2
    );

    // --- Animation state management - using check_state to match CheckboxComponent exactly ---
    static std::map<ImGuiID, check_state> anim;
    auto it_anim = anim.emplace(id, check_state()).first;

    // --- Animation updates - EXACTLY matching CheckboxComponent colors ---
    const float anim_speed = GetAnimSpeed();

    // Use EXACT same animation logic as CheckboxComponent
    it_anim->second.rect_color = ImLerp(it_anim->second.rect_color,
        hovered ? gui.main : func.GetColorWithAlpha(gui.main, 0.3f), anim_speed);
    it_anim->second.rect_shadow_color = ImLerp(it_anim->second.rect_shadow_color,
        hovered ? gui.main : func.GetColorWithAlpha(ImColor(0, 0, 0, 255), 0.3f), anim_speed);
    it_anim->second.icon_color = ImLerp(it_anim->second.icon_color,
        hovered ? func.GetDarkColor(gui.main) : gui.main, anim_speed);
    it_anim->second.background_color = ImLerp(it_anim->second.background_color,
        hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f), anim_speed);
    it_anim->second.background_rect_color = ImLerp(it_anim->second.background_rect_color,
        hovered ? gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f), anim_speed);
    
    // Slider-specific checkbox styling animations
    it_anim->second.check_color = ImLerp(it_anim->second.check_color,
        ImColor(34, 27, 27, 255), anim_speed / 3);
    it_anim->second.check_rect_color = ImLerp(it_anim->second.check_rect_color,
        gui.checkboxstroke, anim_speed / 3);

    // --- Rendering - EXACTLY matching CheckboxComponent structure ---
    // Background - same as CheckboxComponent
    window->DrawList->AddRectFilled(total_bb.Min, total_bb.Max, 
        GetColorU32(it_anim->second.background_color), 3.0f);

    // Border - same as CheckboxComponent
    window->DrawList->AddRect(total_bb.Min, total_bb.Max, 
        GetColorU32(it_anim->second.background_rect_color), 3.0f);

    // Text - same as CheckboxComponent
    window->DrawList->AddText(label_pos, gui.text[0], label);
    window->DrawList->AddText(ImVec2(label_pos.x, label_pos.y + label_size.y + 2), gui.text[1], 
        getStringBeforeCaret(description) != nullptr ? getStringBeforeCaret(description) : description);

    // Left background rectangle with shadow - EXACTLY same as CheckboxComponent
    GetWindowDrawList()->AddRectFilled(total_bb.Min,
        ImVec2(total_bb.Min.x + total_bb.GetSize().y, total_bb.Min.y + total_bb.GetSize().y),
        GetColorU32(it_anim->second.rect_color), style.FrameRounding, ImDrawFlags_RoundCornersLeft);

    window->DrawList->AddShadowRect(total_bb.Min,
        ImVec2(total_bb.Min.x + total_bb.GetSize().y - 5, total_bb.Max.y),
        GetColorU32(it_anim->second.rect_shadow_color), 20.f, ImVec2(0, 0), 0, ImDrawFlags_RoundCornersLeft);

    // Icon shadow - same as CheckboxComponent
    window->DrawList->AddShadowCircle(
        ImVec2(total_bb.Min.x + total_bb.GetSize().y / 2, total_bb.Min.y + total_bb.GetSize().y / 2),
        9.f, GetColorU32(it_anim->second.icon_color), 40, ImVec2(0, 0), 0, 360);

    // --- Slider specific rendering (keeping original slider handle design) ---
    // Slider background matching checkbox style
    window->DrawList->AddRectFilled(slider_bb.Min, slider_bb.Max, 
        GetColorU32(it_anim->second.check_color), 30.f);
    window->DrawList->AddRect(slider_bb.Min, slider_bb.Max, 
        GetColorU32(it_anim->second.check_rect_color), 30.f);

    // Slider behavior
    const bool temp_input_allowed = (flags & ImGuiSliderFlags_NoInput) == 0;
    if (format == NULL) format = DataTypeGetInfo(data_type)->PrintFmt;

    // Set appropriate step size based on data type
    float speed = 1.0f;
    if (data_type == ImGuiDataType_Float) {
        speed = 0.1f; // For floats, use 0.1 increments (1.0, 1.1, 1.2, etc.)
    } else if (data_type == ImGuiDataType_Double) {
        speed = 0.1f; // For doubles, use 0.1 increments
    } else {
        speed = 1.0f; // For integers, use 1.0 increments (1, 2, 3, etc.)
    }

    bool value_changed = DragBehavior(id, data_type, p_data, speed, p_min, p_max, format, flags);
    if (value_changed) {
        MarkItemEdited(id);
    }

    // Handle slider visual effects (keep original bar animation design)
    if (data_type == ImGuiDataType_S32) {
        int* f_data = static_cast<int*>(p_data);
        const int* f_min = static_cast<const int*>(p_min);
        const int* f_max = static_cast<const int*>(p_max);

        const int num_bars = 9;
        float bar_width = 4;
        float bar_spacing = (slider_bb.GetWidth() - num_bars * bar_width) / (num_bars - 1);
        float offset = fmodf(30.0 * (*f_data - *f_min) / static_cast<float>(*f_max - *f_min) * (bar_spacing + bar_width),
            bar_spacing + bar_width);

        for (int i = 0; i < num_bars; ++i) {
            float x = slider_bb.Min.x + i * (bar_width + bar_spacing) - offset;
            float dist_to_mid = std::abs(x - slider_bb.GetCenter().x);
            float height = ImLerp(14.0f, 0.0f, dist_to_mid / (slider_bb.GetWidth() / 1.2f));

            if (x + bar_width < slider_bb.Min.x || x > slider_bb.Max.x) continue;

            ImVec2 min = ImVec2(x, slider_bb.GetCenter().y - height / 2);
            ImVec2 max = ImVec2(x + bar_width, slider_bb.GetCenter().y + height / 2);

            window->DrawList->AddRectFilled(min, max,
                func.GetColorWithAlpha(gui.main,
                    ImLerp(1.0f, 0.0f, dist_to_mid / (slider_bb.GetWidth() / 2.3f))), 30);
        }
    } else if (data_type == ImGuiDataType_Float) {
        float* f_data = static_cast<float*>(p_data);
        const float* f_min = static_cast<const float*>(p_min);
        const float* f_max = static_cast<const float*>(p_max);

        const int num_bars = 9;
        float bar_width = 4;
        float bar_spacing = (slider_bb.GetWidth() - num_bars * bar_width) / (num_bars - 1);
        float offset = fmodf(30.0 * (*f_data - *f_min) / (*f_max - *f_min) * (bar_spacing + bar_width),
            bar_spacing + bar_width);

        for (int i = 0; i < num_bars; ++i) {
            float x = slider_bb.Min.x + i * (bar_width + bar_spacing) - offset;
            float dist_to_mid = std::abs(x - slider_bb.GetCenter().x);
            float height = ImLerp(14.0f, 0.0f, dist_to_mid / (slider_bb.GetWidth() / 1.2f));

            if (x + bar_width < slider_bb.Min.x || x > slider_bb.Max.x) continue;

            ImVec2 min = ImVec2(x, slider_bb.GetCenter().y - height / 2);
            ImVec2 max = ImVec2(x + bar_width, slider_bb.GetCenter().y + height / 2);

            window->DrawList->AddRectFilled(min, max,
                func.GetColorWithAlpha(gui.main,
                    ImLerp(1.0f, 0.0f, dist_to_mid / (slider_bb.GetWidth() / 2.3f))), 30);
        }
    }

    // Value display box (improved styling to match theme)
    char value_buf[64];
    const char* value_buf_end = value_buf + DataTypeFormatString(value_buf, IM_ARRAYSIZE(value_buf), data_type, p_data, format);
    
    const ImRect value_bb(slider_bb.Min - ImVec2(30 + CalcTextSize(value_buf, value_buf_end).x, 0),
        slider_bb.Min - ImVec2(10, -slider_bb.GetSize().y));

    GetWindowDrawList()->AddRectFilled(value_bb.Min, value_bb.Max, 
        func.GetColorWithAlpha(gui.main, 0.3f), 4.f);
    GetWindowDrawList()->AddText(value_bb.GetCenter() - CalcTextSize(value_buf, value_buf_end) / 2,
        gui.main, value_buf, value_buf_end);

    // Additional icon handling (same as original for custom descriptions)
    if (icon != nullptr) {
        PushFont(iconsBig);
        GetWindowDrawList()->AddText(func.CalcTextPos(total_bb.Min,
            total_bb.Min + ImVec2(total_bb.GetSize().y, total_bb.GetSize().y),
            icon),
            GetColorU32(it_anim->second.icon_color),
            icon);
        PopFont();
    }

    return value_changed;
}

bool nav_elements::SliderFloat(const char* label,
    float* v,
    float v_min,
    float v_max,
    const char* description,
    const char* icon,
    const char* format,
    ImGuiSliderFlags flags) {
    return nav_elements::SliderScalar(label, ImGuiDataType_Float, v, &v_min, &v_max, format, flags, description, icon);
}

bool nav_elements::SliderInt(const char* label,
    int* v,
    int v_min,
    int v_max,
    const char* description,
    const char* icon,
    const char* format,
    ImGuiSliderFlags flags) {
    return nav_elements::SliderScalar(label, ImGuiDataType_S32, v, &v_min, &v_max, format, flags, description, icon);
}


struct KeybindState {
    ImVec4 check_color;
    ImVec4 check_color_circle;
    ImVec4 rect_color;
    ImVec4 rect_shadow_color;
    ImVec4 check_rect_color;
    float circle_alpha;
    ImVec4 icon_color;
    float element_opacity;
    ImVec4 background_color;
    ImVec4 background_rect_color;

    // Animation specific to keybind
    float activation_anim = 0.0f;
    ImVec4 press_indicator_color = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);

    KeybindState() {
        reset();
    }

    void reset() {
        check_color = ImVec4(0.13f, 0.13f, 0.13f, 1.00f);
        check_color_circle = ImVec4(0.25f, 0.25f, 0.25f, 1.00f);
        rect_color = ImVec4(0.20f, 0.20f, 0.20f, 1.00f);
        rect_shadow_color = ImVec4(0.00f, 0.00f, 0.00f, 0.30f);
        check_rect_color = ImVec4(0.13f, 0.13f, 0.13f, 1.00f);
        circle_alpha = 0.6f;
        icon_color = ImVec4(1.00f, 1.00f, 1.00f, 1.00f);
        element_opacity = 0.2f;
        background_color = ImVec4(0.09f, 0.07f, 0.06f, 0.61f);
        background_rect_color = ImVec4(0.13f, 0.13f, 0.13f, 0.30f);
        activation_anim = 0.0f;
        press_indicator_color = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);
    }
};


// Helper easing
static float EaseOutExpo(float t) {
    t = ImClamp(t, 0.0f, 1.0f);
    return (t >= 1.0f) ? 1.0f : 1.0f - powf(2.0f, -10.0f * t);
}

struct KeybindAnimEx {
    float reset_hover_alpha = 0.0f;
    float reset_time = 0.0f;

    float thumb_t = 0.0f;      // 0 (Toggle/left) .. 1 (Hold/right)
    float thumb_target = 0.0f;
    float thumb_time = 0.0f;
};

bool nav_elements::Keybind(const char* label, const char* description, const char* icon, int* key, bool* mode)
{
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems || !label || !description || !key)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;

    const ImGuiID id = window->GetID(label);
    const std::string featureName = std::string(label);
    const ImGuiID row_id = id;
    const ImGuiID key_id = window->GetID((featureName + "##key").c_str());
    const ImGuiID mode_id = window->GetID((featureName + "##mode").c_str());
    const ImGuiID reset_id = window->GetID((featureName + "##reset").c_str());

    const ImVec2 pos = window->DC.CursorPos;
    const float content_width = ImGui::GetContentRegionAvail().x;
    const float component_height = 60.f;
    const ImRect total_bb(pos, ImVec2(pos.x + content_width, pos.y + component_height));
    ImGui::ItemSize(total_bb, 0.f);
    if (!ImGui::ItemAdd(total_bb, row_id))
        return false;

    bool hovered = false, held = false;
    bool row_pressed = ImGui::ButtonBehavior(total_bb, row_id, &hovered, &held);

    const ImVec2 description_size = ImGui::CalcTextSize(description, NULL, true);
    const ImVec2 label_size = ImGui::CalcTextSize(label, NULL, true);
    const ImVec2 label_pos = ImVec2(
        total_bb.Min.x + 60.f + (total_bb.GetSize().y - (label_size.y + description_size.y)) / 2,
        total_bb.GetCenter().y - (label_size.y + description_size.y) / 2
    );

    // Checkbox-like color animation
    static std::map<ImGuiID, check_state> anim_like_checkbox;
    auto it_anim = anim_like_checkbox.emplace(row_id, check_state()).first;
    const float anim_speed = GetAnimSpeed();

    it_anim->second.check_color = ImLerp(it_anim->second.check_color,
        hovered ? gui.checkboxstroke : ImColor(34, 27, 27, 255), anim_speed / 3);
    it_anim->second.check_rect_color = ImLerp(it_anim->second.check_rect_color,
        hovered ? gui.checkboxstrokeactive : gui.checkboxstroke, anim_speed / 3);
    it_anim->second.rect_color = ImLerp(it_anim->second.rect_color,
        hovered ? gui.main : func.GetColorWithAlpha(gui.main, 0.3f), anim_speed);
    it_anim->second.rect_shadow_color = ImLerp(it_anim->second.rect_shadow_color,
        hovered ? gui.main : func.GetColorWithAlpha(ImColor(0, 0, 0, 255), 0.3f), anim_speed);
    it_anim->second.icon_color = ImLerp(it_anim->second.icon_color,
        hovered ? func.GetDarkColor(gui.main) : gui.main, anim_speed);
    it_anim->second.background_color = ImLerp(it_anim->second.background_color,
        hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f), anim_speed);
    it_anim->second.background_rect_color = ImLerp(it_anim->second.background_rect_color,
        hovered ? gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f), anim_speed);

    // Extended per-row animation
    static std::map<ImGuiID, KeybindAnimEx> ex_anim;
    KeybindAnimEx& ex = ex_anim[row_id];

    // Row visuals
    window->DrawList->AddRectFilled(total_bb.Min, total_bb.Max, ImGui::GetColorU32(it_anim->second.background_color), 3.0f);
    window->DrawList->AddRect(total_bb.Min, total_bb.Max, ImGui::GetColorU32(it_anim->second.background_rect_color), 3.0f);

    // Left panel + shadow
    const ImRect left_panel_bb(
        total_bb.Min,
        ImVec2(total_bb.Min.x + total_bb.GetSize().y, total_bb.Min.y + total_bb.GetSize().y)
    );
    window->DrawList->AddRectFilled(left_panel_bb.Min, left_panel_bb.Max,
        ImGui::GetColorU32(it_anim->second.rect_color), style.FrameRounding, ImDrawFlags_RoundCornersLeft);
    window->DrawList->AddShadowRect(
        left_panel_bb.Min, ImVec2(left_panel_bb.Max.x - 5, left_panel_bb.Max.y),
        ImGui::GetColorU32(it_anim->second.rect_shadow_color), 20.f, ImVec2(0, 0), 0, ImDrawFlags_RoundCornersLeft);

    // Icon (optional)
    if (icon != nullptr) {
        PushFont(iconsBig);
        window->DrawList->AddText(
            func.CalcTextPos(total_bb.Min,
                ImVec2(total_bb.Min.x + total_bb.GetSize().y, total_bb.Min.y + total_bb.GetSize().y),
                icon),
            GetColorU32(it_anim->second.icon_color),
            icon);
        PopFont();
    }

    // Labels
    window->DrawList->AddText(label_pos, gui.text[0], label);
    window->DrawList->AddText(ImVec2(label_pos.x, label_pos.y + label_size.y + 2), gui.text[1], description);

    // Right-side summary pill (non-clickable)
    {
        const float pill_h = 26.f;
        const float pill_w = 120.f;
        const float pad_r = 15.f;
        ImRect pill_bb(
            ImVec2(total_bb.Max.x - pad_r - pill_w, total_bb.Min.y + (total_bb.GetHeight() - pill_h) * 0.5f),
            ImVec2(total_bb.Max.x - pad_r, total_bb.Min.y + (total_bb.GetHeight() + pill_h) * 0.5f)
        );
        window->DrawList->AddRectFilled(pill_bb.Min, pill_bb.Max, ImGui::GetColorU32(it_anim->second.check_color), 30.f);
        window->DrawList->AddRect(pill_bb.Min, pill_bb.Max, ImGui::GetColorU32(it_anim->second.check_rect_color), 30.f);
        window->DrawList->AddShadowCircle(pill_bb.GetCenter(), 8.f, ImGui::GetColorU32(it_anim->second.check_color), 15.f, ImVec2(0, 0), 0, 360);

        char buf_display[64] = "None";
        if (*key != 0) strcpy_s(buf_display, InputManager::GetKeyName(*key).c_str());
        ImVec2 tsz = ImGui::CalcTextSize(buf_display);
        ImVec2 tpos(pill_bb.GetCenter().x - tsz.x * 0.5f, pill_bb.GetCenter().y - tsz.y * 0.5f);
        window->DrawList->AddText(tpos, ImGui::GetColorU32(ImVec4(1, 1, 1, 1)), buf_display);
    }

    // Popup opening tracking
    static std::map<std::string, bool> popupActiveMap;
    static std::map<std::string, int>  popupOpenFrameMap;
    auto& isPopupActive = popupActiveMap[featureName];
    if (row_pressed) {
        // Prevent rapid clicking during popup operations
        static std::map<std::string, float> lastKeybindClickTime;
        float currentTime = ImGui::GetTime();
        
        if (lastKeybindClickTime.find(featureName) == lastKeybindClickTime.end() || 
            (currentTime - lastKeybindClickTime[featureName]) > 0.2f) { // 200ms debounce
            
            isPopupActive = true;
            gui.darkoverlay = true;
            gui.picker_active = true;
            
            // Ensure dark overlay stays active for minimum time
            static std::map<std::string, float> keybindDarkOverlayStartTime;
            keybindDarkOverlayStartTime[featureName] = currentTime;
            
            ImGui::OpenPopup(("##KeybindPopup_" + featureName).c_str());
            popupOpenFrameMap[featureName] = g.FrameCount;
            
            lastKeybindClickTime[featureName] = currentTime;
        }
    }

    // Popup styling
    ImGui::PushStyleColor(ImGuiCol_WindowBg, func.ImColorToImVec4(gui.window_bg));
    ImGui::PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.window_bg));
    ImGui::PushStyleColor(ImGuiCol_Border, func.ImColorToImVec4(gui.stroke));
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, style.FrameRounding);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 1.f);

    // Center popup
    ImVec2 center(gui.window_pos.x + gui.window_size.x * 0.5f, gui.window_pos.y + gui.window_size.y * 0.5f);
    ImGui::SetNextWindowPos(center, ImGuiCond_Always, ImVec2(0.5f, 0.5f));
    const std::string popup_id = "##KeybindPopup_" + featureName;

    // Persistent capture state KEY: fix for "<Press>" disappearing
    static std::map<ImGuiID, bool> capturing_states;

    if (ImGui::BeginPopup(popup_id.c_str(), ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoTitleBar))
    {
        ImGuiWindow* popup_window = ImGui::GetCurrentWindow();
        ImDrawList* dl = ImGui::GetWindowDrawList();
        const bool justOpened = (popupOpenFrameMap[featureName] == g.FrameCount);

        // Avoid closing on the same click as opening
        if (!justOpened &&
            ImGui::IsMouseClicked(ImGuiMouseButton_Left) &&
            !ImGui::IsWindowHovered(ImGuiHoveredFlags_AnyWindow | ImGuiHoveredFlags_AllowWhenBlockedByPopup) &&
            !ImGui::IsAnyItemHovered())
        {
            isPopupActive = false;
            gui.darkoverlay = false;
            gui.picker_active = false;
            if (capturing_states[key_id]) { ImGui::ClearActiveID(); capturing_states[key_id] = false; }
            ImGui::CloseCurrentPopup();
        }

        // Close on Esc only if not capturing
        if (ImGui::IsKeyPressed(ImGuiKey_Escape) && !capturing_states[key_id]) {
            isPopupActive = false;
            gui.darkoverlay = false;
            gui.picker_active = false;
            ImGui::CloseCurrentPopup();
        }

        ImGui::BeginGroup();

        const float btn_width = 220.f;
        const float btn_height = 34.f;
        const float spacing = 10.f;
        const float rounding = 30.f;

        ImVec2 base = ImGui::GetCursorScreenPos();

        // Hotkey button
        ImRect key_btn_bb(base, ImVec2(base.x + btn_width, base.y + btn_height));
        ImGui::Dummy(ImVec2(btn_width, btn_height));

        bool key_hovered = ImGui::IsMouseHoveringRect(key_btn_bb.Min, key_btn_bb.Max);
        bool key_held = false;
        bool key_button_pressed = ImGui::ButtonBehavior(key_btn_bb, key_id, &key_hovered, &key_held, ImGuiButtonFlags_PressedOnClick);

        // Start capture: set ActiveID and persist flag (DO NOT clear it elsewhere)
        if (key_button_pressed && !capturing_states[key_id]) {
            ImGui::SetActiveID(key_id, popup_window);
            ImGui::FocusWindow(popup_window);
            capturing_states[key_id] = true;
        }

        // While capturing: keep "<Press>" visible until success or Esc
        if (capturing_states[key_id]) {
            if (ImGui::IsKeyPressed(ImGuiKey_Escape)) {
                ImGui::ClearActiveID();
                capturing_states[key_id] = false;
            }
            else {
                int pressed_key = InputManager::GetPressedKey();
                if (pressed_key != 0 && pressed_key != VK_ESCAPE) {
                    *key = pressed_key;
                    SettingsHelper::SaveSettings();
                    ImGui::ClearActiveID();
                    capturing_states[key_id] = false;
                }
            }
        }

        // Draw Hotkey button (blue when capturing)
        ImU32 key_bg = ImGui::GetColorU32(it_anim->second.check_color);
        if (capturing_states[key_id]) {
            key_bg = gui.main_for_border;
        }
        dl->AddRectFilled(key_btn_bb.Min, key_btn_bb.Max, key_bg, rounding);
        dl->AddRect(key_btn_bb.Min, key_btn_bb.Max, ImGui::GetColorU32(it_anim->second.check_rect_color), rounding);
        dl->AddShadowCircle(key_btn_bb.GetCenter(), 8.f, ImGui::GetColorU32(it_anim->second.check_color), 15.f, ImVec2(0, 0), 0, 360);

        char key_text[64] = "None";
        if (capturing_states[key_id]) {
            strcpy_s(key_text, "<Press>");
        }
        else if (*key != 0) {
            strcpy_s(key_text, InputManager::GetKeyName(*key).c_str());
        }
        ImVec2 key_txt_sz = ImGui::CalcTextSize(key_text);
        ImVec2 key_txt_pos(key_btn_bb.GetCenter().x - key_txt_sz.x * 0.5f, key_btn_bb.GetCenter().y - key_txt_sz.y * 0.5f);
        dl->AddText(key_txt_pos, ImGui::GetColorU32(ImVec4(1, 1, 1, 1)), key_text);

        // Mode (Toggle/Hold). Backend mapping: false = Toggle (left), true = Hold (right).
        ImGui::SetCursorScreenPos(ImVec2(base.x, base.y + btn_height + spacing));
        ImVec2 row2_start = ImGui::GetCursorScreenPos();
        ImRect mode_bb(row2_start, ImVec2(row2_start.x + btn_width, row2_start.y + btn_height));
        ImGui::Dummy(ImVec2(btn_width, btn_height));

        if (mode) {
            bool mode_hovered = ImGui::IsMouseHoveringRect(mode_bb.Min, mode_bb.Max);
            bool mode_held = false;
            bool mode_pressed = ImGui::ButtonBehavior(mode_bb, mode_id, &mode_hovered, &mode_held, ImGuiButtonFlags_PressedOnClick);
            if (mode_pressed) {
                *mode = !*mode;
                SettingsHelper::SaveSettings();
            }

            // Animate thumb (slide) and water effect
            ex.thumb_target = (*mode) ? 1.0f : 0.0f;
            float dt = ImGui::GetIO().DeltaTime;
            float s = 1.0f - powf(1.0f - 12.0f * dt, 1.0f);
            ex.thumb_t = ImLerp(ex.thumb_t, ex.thumb_target, s);
            ex.thumb_time += dt;

            // Track
            dl->AddRectFilled(mode_bb.Min, mode_bb.Max, ImGui::GetColorU32(it_anim->second.check_color), rounding);
            dl->AddRect(mode_bb.Min, mode_bb.Max, ImGui::GetColorU32(it_anim->second.check_rect_color), rounding);
            dl->AddShadowCircle(mode_bb.GetCenter(), 8.f, ImGui::GetColorU32(it_anim->second.check_color), 15.f, ImVec2(0, 0), 0, 360);

            // Thumb (transparent blue)
            const float seg_padding = 4.f;
            ImRect track_bb(ImVec2(mode_bb.Min.x + seg_padding, mode_bb.Min.y + seg_padding),
                ImVec2(mode_bb.Max.x - seg_padding, mode_bb.Max.y - seg_padding));
            float track_w = track_bb.GetWidth();
            float thumb_w = track_w * 0.5f;
            float thumb_x0 = track_bb.Min.x + ex.thumb_t * (track_w - thumb_w);
            ImRect thumb_bb(ImVec2(thumb_x0, track_bb.Min.y), ImVec2(thumb_x0 + thumb_w, track_bb.Max.y));

            dl->AddRectFilled(thumb_bb.Min, thumb_bb.Max, gui.main_for_border, rounding);

            // Labels always white
            const char* left_label = "Hold";
            const char* right_label = "Toggle";
            ImVec2 lt_sz = ImGui::CalcTextSize(left_label);
            ImVec2 rt_sz = ImGui::CalcTextSize(right_label);
            ImVec2 lt_pos(track_bb.Min.x + (thumb_w - lt_sz.x) * 0.5f, mode_bb.GetCenter().y - lt_sz.y * 0.5f);
            ImVec2 rt_pos(track_bb.Min.x + thumb_w + (thumb_w - rt_sz.x) * 0.5f, mode_bb.GetCenter().y - rt_sz.y * 0.5f);
            ImU32 text_white = ImGui::GetColorU32(ImVec4(1, 1, 1, 1));
            dl->AddText(lt_pos, text_white, left_label);
            dl->AddText(rt_pos, text_white, right_label);
        }

        // Reset button
        ImGui::SetCursorScreenPos(ImVec2(base.x, base.y + 2 * btn_height + 2 * spacing));
        ImVec2 reset_pos = ImGui::GetCursorScreenPos();
        ImRect reset_bb(reset_pos, ImVec2(reset_pos.x + btn_width, reset_pos.y + btn_height));
        ImGui::Dummy(ImVec2(btn_width, btn_height));

        bool reset_hovered = ImGui::IsMouseHoveringRect(reset_bb.Min, reset_bb.Max);
        bool reset_held = false;
        bool reset_pressed = ImGui::ButtonBehavior(reset_bb, reset_id, &reset_hovered, &reset_held, ImGuiButtonFlags_PressedOnClick);
        if (reset_pressed) {
            *key = 0;
            if (capturing_states[key_id]) {
                ImGui::ClearActiveID();
                capturing_states[key_id] = false;
            }
            SettingsHelper::SaveSettings();
        }

        // Hover animation for Reset
        float dt2 = ImGui::GetIO().DeltaTime;
        ex.reset_time += dt2;
        float target_alpha = reset_hovered ? 1.0f : 0.0f;
        ex.reset_hover_alpha = ImLerp(ex.reset_hover_alpha, target_alpha, 1.0f - powf(1.0f - 12.0f * dt2, 1.0f));

        // Draw Reset (base + water effect)
        dl->AddRectFilled(reset_bb.Min, reset_bb.Max, ImGui::GetColorU32(it_anim->second.check_color), 30.f);
        dl->AddRect(reset_bb.Min, reset_bb.Max, ImGui::GetColorU32(it_anim->second.check_rect_color), 30.f);
        dl->AddShadowCircle(reset_bb.GetCenter(), 8.f, ImGui::GetColorU32(it_anim->second.check_color), 15.f, ImVec2(0, 0), 0, 360);

        if (ex.reset_hover_alpha > 0.01f) {
            ImVec4 red_fill = ImVec4(0.25f, 0.25f, 0.95f, 0.35f * ex.reset_hover_alpha);
            dl->AddRectFilled(reset_bb.Min, reset_bb.Max, ImGui::GetColorU32(red_fill), 30.f);
        }

        const char* reset_text = "Reset";
        ImVec2 r_sz = ImGui::CalcTextSize(reset_text);
        ImVec2 r_pos(reset_bb.GetCenter().x - r_sz.x * 0.5f, reset_bb.GetCenter().y - r_sz.y * 0.5f);
        dl->AddText(r_pos, ImGui::GetColorU32(ImVec4(1, 1, 1, 1)), reset_text);

        ImGui::EndGroup();
        ImGui::EndPopup();
    }
    else {
        // If popup is not open but we considered it active, reset overlay state
        if (isPopupActive && !ImGui::IsPopupOpen(popup_id.c_str())) {
            isPopupActive = false;
            gui.darkoverlay = false;
            gui.picker_active = false;
        }
    }

    ImGui::PopStyleVar(3);
    ImGui::PopStyleColor(3);

    return true;
}




static void ColorEditRestoreH(const float* col, float* H) {
    ImGuiContext& g = *GImGui;
    IM_ASSERT(g.ColorEditCurrentID != 0);
    if (g.ColorEditSavedID != g.ColorEditCurrentID ||
        g.ColorEditSavedColor != ImGui::ColorConvertFloat4ToU32(ImVec4(col[0], col[1], col[2], 0)))
        return;
    *H = g.ColorEditSavedHue;
}

static void ColorEditRestoreHS(const float* col, float* H, float* S, float* V) {
    ImGuiContext& g = *GImGui;
    IM_ASSERT(g.ColorEditCurrentID != 0);
    if (g.ColorEditSavedID != g.ColorEditCurrentID ||
        g.ColorEditSavedColor != ImGui::ColorConvertFloat4ToU32(ImVec4(col[0], col[1], col[2], 0)))
        return;

    if (*S == 0.0f || (*H == 0.0f && g.ColorEditSavedHue == 1))
        *H = g.ColorEditSavedHue;

    if (*V == 0.0f) *S = g.ColorEditSavedSat;
}


struct edit_state {
    ImVec4 text;
    float alpha = 0.f, alpha_search;
    bool hovered, colors_hovered, active = false;
    ImVec2 picker_offset{ 0, 200.f };
};


bool nav_elements::ColorEdit4Ex(const char* label, const char* description, float col[4], ImGuiColorEditFlags flags) {
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems) return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const float square_sz = 18.f;
    const float w_full = 60.f;
    const float w_button = (flags & ImGuiColorEditFlags_NoSmallPreview) ? 0.0f : square_sz;
    const float w_inputs = w_full - w_button;
    const char* label_display_end = FindRenderedTextEnd(label);
    g.NextItemData.ClearFlags();

    char buf[64];
    static bool search_col = false;

    BeginGroup();
    PushID(label);
    const bool set_current_color_edit_id = (g.ColorEditCurrentID == 0);
    if (set_current_color_edit_id) g.ColorEditCurrentID = window->IDStack.back();

    const ImGuiColorEditFlags flags_untouched = flags;

    if (flags & ImGuiColorEditFlags_NoInputs)
        flags = (flags & (~ImGuiColorEditFlags_DisplayMask_)) | ImGuiColorEditFlags_DisplayRGB |
        ImGuiColorEditFlags_NoOptions;

    if (!(flags & ImGuiColorEditFlags_NoOptions)) ColorEditOptionsPopup(col, flags);

    if (!(flags & ImGuiColorEditFlags_DisplayMask_))
        flags |= (g.ColorEditOptions & ImGuiColorEditFlags_DisplayMask_);
    if (!(flags & ImGuiColorEditFlags_DataTypeMask_))
        flags |= (g.ColorEditOptions & ImGuiColorEditFlags_DataTypeMask_);
    if (!(flags & ImGuiColorEditFlags_PickerMask_))
        flags |= (g.ColorEditOptions & ImGuiColorEditFlags_PickerMask_);
    if (!(flags & ImGuiColorEditFlags_InputMask_))
        flags |= (g.ColorEditOptions & ImGuiColorEditFlags_InputMask_);
    flags |= (g.ColorEditOptions &
        ~(ImGuiColorEditFlags_DisplayMask_ | ImGuiColorEditFlags_DataTypeMask_ | ImGuiColorEditFlags_PickerMask_ |
            ImGuiColorEditFlags_InputMask_));
    IM_ASSERT(ImIsPowerOfTwo(flags & ImGuiColorEditFlags_DisplayMask_));
    IM_ASSERT(ImIsPowerOfTwo(flags & ImGuiColorEditFlags_InputMask_));

    const bool alpha = (flags & ImGuiColorEditFlags_NoAlpha) == 0;
    const bool hdr = (flags & ImGuiColorEditFlags_HDR) != 0;
    const int components = alpha ? 4 : 3;

    float f[4] = { col[0], col[1], col[2], alpha ? col[3] : 1.0f };
    if ((flags & ImGuiColorEditFlags_InputHSV) && (flags & ImGuiColorEditFlags_DisplayRGB))
        ColorConvertHSVtoRGB(f[0], f[1], f[2], f[0], f[1], f[2]);
    else if ((flags & ImGuiColorEditFlags_InputRGB) && (flags & ImGuiColorEditFlags_DisplayHSV)) {
        ColorConvertRGBtoHSV(f[0], f[1], f[2], f[0], f[1], f[2]);
        ColorEditRestoreHS(col, &f[0], &f[1], &f[2]);
    }
    int i[4] = { IM_F32_TO_INT8_UNBOUND(f[0]), IM_F32_TO_INT8_UNBOUND(f[1]), IM_F32_TO_INT8_UNBOUND(f[2]),
                IM_F32_TO_INT8_UNBOUND(f[3]) };

    bool value_changed = false;
    bool value_changed_as_float = false;

    const ImVec2 pos = window->DC.CursorPos;
    const float inputs_offset_x = (style.ColorButtonPosition == ImGuiDir_Left) ? w_button : 0.0f;
    window->DC.CursorPos.x = pos.x + inputs_offset_x;

    if ((flags & (ImGuiColorEditFlags_DisplayRGB | ImGuiColorEditFlags_DisplayHSV)) != 0 &&
        (flags & ImGuiColorEditFlags_NoInputs) == 0) {
        const float w_item_one = ImMax(1.0f,
            IM_FLOOR((w_inputs - (style.ItemInnerSpacing.x) * (components - 1)) /
                (float)components));
        const float w_item_last = ImMax(1.0f,
            IM_FLOOR(
                w_inputs - (w_item_one + style.ItemInnerSpacing.x) * (components - 1)));

        const bool hide_prefix = (w_item_one <=
            CalcTextSize((flags & ImGuiColorEditFlags_Float) ? "M:0.000" : "M:000").x);
        static const char* ids[4] = { "##X", "##Y", "##Z", "##W" };
        static const char* fmt_table_int[3][4] =
        {
            {"%3d", "%3d", "%3d", "%3d"},
            {"R:%3d", "G:%3d", "B:%3d", "A:%3d"},
            {"H:%3d", "S:%3d", "V:%3d", "A:%3d"}
        };

        static const char* fmt_table_float[3][4] =
        {
            {"%0.3f", "%0.3f", "%0.3f", "%0.3f"},
            {"R:%0.3f", "G:%0.3f", "B:%0.3f", "A:%0.3f"},
            {"H:%0.3f", "S:%0.3f", "V:%0.3f", "A:%0.3f"}
        };

        const int fmt_idx = hide_prefix ? 0 : (flags & ImGuiColorEditFlags_DisplayHSV) ? 2 : 1;

        for (int n = 0; n < components; n++) {
            if (n > 0) SameLine(0, style.ItemInnerSpacing.x);
            SetNextItemWidth((n + 1 < components) ? w_item_one : w_item_last);

            if (flags & ImGuiColorEditFlags_Float) {
                value_changed |= DragFloat(ids[n],
                    &f[n],
                    1.0f / 255.0f,
                    0.0f,
                    hdr ? 0.0f : 1.0f,
                    fmt_table_float[fmt_idx][n]);
                value_changed_as_float |= value_changed;
            }
            else {
                value_changed |= DragInt(ids[n], &i[n], 1.0f, 0, hdr ? 0 : 255, fmt_table_int[fmt_idx][n]);
            }
            if (!(flags & ImGuiColorEditFlags_NoOptions))
                OpenPopupOnItemClick("context",
                    ImGuiPopupFlags_MouseButtonRight);
        }
    }

    static std::map<ImGuiID, edit_state> anim;
    edit_state& state = anim[ImGui::GetID(label)];

    ImGuiWindow* picker_active_window = NULL;
    if (!(flags & ImGuiColorEditFlags_NoSmallPreview)) {
        const float button_offset_x = ((flags & ImGuiColorEditFlags_NoInputs) ||
            (style.ColorButtonPosition == ImGuiDir_Left)) ? 0.0f : w_inputs +
            style.ItemInnerSpacing.x;
        window->DC.CursorPos = ImVec2(pos.x + button_offset_x, pos.y);

        const ImVec4 col_v4(col[0], col[1], col[2], alpha ? col[3] : 1.0f);

        // Directly open the popup when the ColorButton is clicked
        if (nav_elements::ColorButton("##ColorButton", col_v4, flags, ImVec2(20, 20))) {
            if (!(flags & ImGuiColorEditFlags_NoPicker)) {
                state.active = true;
                // Only set picker_active when actually opening the popup, not on hover
                gui.picker_active = true;
                g.ColorPickerRef = col_v4;
                OpenPopup("color_picker");
                SetNextWindowPos(g.LastItemData.Rect.GetBL() + ImVec2(0.0f, style.ItemSpacing.y));
            }
        }

        if (!(flags & ImGuiColorEditFlags_NoOptions)) OpenPopupOnItemClick("context", ImGuiPopupFlags_MouseButtonRight);

        state.alpha_search = ImLerp(state.alpha_search, search_col ? 1.f : 0.f, g.IO.DeltaTime * 6.f);

        PushStyleColor(ImGuiCol_WindowBg, func.ImColorToImVec4(gui.window_bg));
        PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.window_bg));
        PushStyleColor(ImGuiCol_Border, func.ImColorToImVec4(gui.stroke));

        PushStyleVar(ImGuiStyleVar_WindowRounding, style.FrameRounding);
        PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
        PushStyleVar(ImGuiStyleVar_WindowBorderSize, 1.f);

        POINT cursorPos;
        COLORREF color;


        std::string label_str = label;

        if (state.active && gui.picker_active)
            EasingAnimationV2("picker_state" + label_str,
                &state.picker_offset,
                ImVec2(0.f, 0.f),
                0.01f,
                imanim::EasingCurve::Type::OutInBack,
                -1);
        else
            EasingAnimationV2("picker_state" + label_str,
                &state.picker_offset,
                ImVec2(0.f, 0.f),
                0.6f,
                imanim::EasingCurve::Type::OutInQuad,
                -1);

        SetNextWindowPos(gui.window_pos + gui.window_size / 2 - ImVec2(128.5, 126) + state.picker_offset);

        if (BeginPopup("color_picker",
            ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_NoMove | ImGuiWindowFlags_AlwaysAutoResize /*|
            ImGuiWindowFlags_NoFocusOnAppearing */ | ImGuiWindowFlags_NoNavFocus)) {
            state.hovered = ImGui::IsMouseHoveringRect(ImGui::GetWindowPos(),
                ImGui::GetWindowPos() + ImGui::GetWindowSize());

            if (state.hovered)
                ImGui::SetWindowFocus();

            if (!state.colors_hovered && !state.hovered && ImGui::IsMouseClicked(ImGuiMouseButton_Left) || ImGui::IsKeyPressed(ImGuiKey_Escape)) {
                state.active = false;
                CloseCurrentPopup();
                gui.picker_active = false;
            }

            state.active = true;

            gui.picker_window = ImGui::GetCurrentWindow();

            ImVec4 col_v4(col[0], col[1], col[2], (flags & ImGuiColorEditFlags_NoAlpha) ? 1.0f : col[3]);

            GetCursorPos(&cursorPos);
            HDC hdc = GetDC(NULL);
            color = GetPixel(hdc, cursorPos.x, cursorPos.y);

            if (search_col) {

                static DWORD dwTickStart = GetTickCount();
                if (GetTickCount() - dwTickStart > 150) {
                    col[0] = GetRValue(color) / 255.f;
                    col[1] = GetGValue(color) / 255.f;
                    col[2] = GetBValue(color) / 255.f;
                    dwTickStart = GetTickCount();

                    if (GetAsyncKeyState(VK_LBUTTON) & 0x8000) search_col = false;

                }
            }

            if (alpha)
                ImFormatString(buf,
                    IM_ARRAYSIZE(buf),
                    "#%02X%02X%02X%02X",
                    ImClamp(i[0], 0, 255),
                    ImClamp(i[1], 0, 255),
                    ImClamp(i[2], 0, 255),
                    ImClamp(i[3], 0, 255));
            else
                ImFormatString(buf,
                    IM_ARRAYSIZE(buf),
                    "#%02X%02X%02X",
                    ImClamp(i[0], 0, 255),
                    ImClamp(i[1], 0, 255),
                    ImClamp(i[2], 0, 255));

            picker_active_window = g.CurrentWindow;
            ImGuiColorEditFlags picker_flags_to_forward =
                ImGuiColorEditFlags_DataTypeMask_ | ImGuiColorEditFlags_PickerMask_ | ImGuiColorEditFlags_InputMask_ |
                ImGuiColorEditFlags_HDR | ImGuiColorEditFlags_NoAlpha | ImGuiColorEditFlags_AlphaBar;
            ImGuiColorEditFlags picker_flags =
                (flags_untouched & picker_flags_to_forward) | ImGuiColorEditFlags_DisplayMask_ |
                ImGuiColorEditFlags_NoLabel | ImGuiColorEditFlags_AlphaPreviewHalf;

            SetNextItemWidth(square_sz * 11.5f);
            value_changed |= ColorPicker4("##picker", col, picker_flags, &g.ColorPickerRef.x);

            PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(0, 2));
            if (InputTextEx("v",
                "HEX COLOR",
                buf,
                IM_ARRAYSIZE(buf),
                ImVec2(233, 35),
                ImGuiInputTextFlags_CharsHexadecimal | ImGuiInputTextFlags_CharsUppercase)) {
                value_changed = true;
                char* p = buf;
                while (*p == '#' || ImCharIsBlankA(*p)) p++;
                i[0] = i[1] = i[2] = 0;
                i[3] = 0xFF;
                int r;
                if (alpha)
                    r = sscanf_s(p,
                        "%02X%02X%02X%02X",
                        (unsigned int*)&i[0],
                        (unsigned int*)&i[1],
                        (unsigned int*)&i[2],
                        (unsigned int*)&i[3]); // Treat at unsigned (%X is unsigned)
                else
                    r = sscanf_s(p,
                        "%02X%02X%02X",
                        (unsigned int*)&i[0],
                        (unsigned int*)&i[1],
                        (unsigned int*)&i[2]);
                IM_UNUSED(r);
            }
            PopStyleVar();
            EndPopup();
        }
    }
    PopStyleColor(3);
    PopStyleVar(3);

    PopID();
    EndGroup();

    return value_changed;
}

static void RenderArrowsForVerticalBar(ImDrawList* draw_list, ImVec2 pos, ImVec2 half_sz, float bar_w, float alpha) {
    ImU32 alpha8 = IM_F32_TO_INT8_SAT(alpha);
    ImGui::RenderArrowPointingAt(draw_list,
        ImVec2(pos.x + half_sz.x + 1, pos.y),
        ImVec2(half_sz.x + 2, half_sz.y + 1),
        ImGuiDir_Right,
        IM_COL32(0, 0, 0, alpha8));
    ImGui::RenderArrowPointingAt(draw_list,
        ImVec2(pos.x + half_sz.x, pos.y),
        half_sz,
        ImGuiDir_Right,
        IM_COL32(255, 255, 255, alpha8));
    ImGui::RenderArrowPointingAt(draw_list,
        ImVec2(pos.x + bar_w - half_sz.x - 1, pos.y),
        ImVec2(half_sz.x + 2, half_sz.y + 1),
        ImGuiDir_Left,
        IM_COL32(0, 0, 0, alpha8));
    ImGui::RenderArrowPointingAt(draw_list,
        ImVec2(pos.x + bar_w - half_sz.x, pos.y),
        half_sz,
        ImGuiDir_Left,
        IM_COL32(255, 255, 255, alpha8));
}

struct picker_state {
    float hue_bar;
    float alpha_bar;
    float circle, color_alpha;
    ImVec2 circle_move;
};

// Global state for CheckboxComponent
struct CheckboxComponentState {
    ImGuiID color_edit_id = 0;
    int color_edit_index = -1;
} g_CheckboxComponentState;

bool nav_elements::ColorPicker4(const char* label, float col[4], ImGuiColorEditFlags flags, const float* ref_col) {
    ImGuiContext& g = *GImGui;
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImDrawList* draw_list = window->DrawList;
    ImGuiStyle& style = g.Style;
    ImGuiIO& io = g.IO;

    const float width = CalcItemWidth();
    g.NextItemData.ClearFlags();

    PushID(label);
    const bool set_current_color_edit_id = (g.ColorEditCurrentID == 0);
    if (set_current_color_edit_id)
        g.ColorEditCurrentID = window->IDStack.back();
    BeginGroup();

    if (!(flags & ImGuiColorEditFlags_NoSidePreview)) flags |= ImGuiColorEditFlags_NoSmallPreview;

    if (!(flags & ImGuiColorEditFlags_NoOptions)) ColorPickerOptionsPopup(col, flags);

    if (!(flags & ImGuiColorEditFlags_PickerMask_))
        flags |= ((g.ColorEditOptions & ImGuiColorEditFlags_PickerMask_) ? g.ColorEditOptions
            : ImGuiColorEditFlags_DefaultOptions_) &
        ImGuiColorEditFlags_PickerMask_;
    if (!(flags & ImGuiColorEditFlags_InputMask_))
        flags |= ((g.ColorEditOptions & ImGuiColorEditFlags_InputMask_) ? g.ColorEditOptions
            : ImGuiColorEditFlags_DefaultOptions_) &
        ImGuiColorEditFlags_InputMask_;
    IM_ASSERT(ImIsPowerOfTwo(flags & ImGuiColorEditFlags_PickerMask_));
    IM_ASSERT(ImIsPowerOfTwo(flags & ImGuiColorEditFlags_InputMask_));
    if (!(flags & ImGuiColorEditFlags_NoOptions)) flags |= (g.ColorEditOptions & ImGuiColorEditFlags_AlphaBar);

    int components = (flags & ImGuiColorEditFlags_NoAlpha) ? 3 : 4;
    bool alpha_bar = (flags & ImGuiColorEditFlags_AlphaBar) && !(flags & ImGuiColorEditFlags_NoAlpha);
    ImVec2 bars_pos = window->DC.CursorPos;
    float square_sz = GetFrameHeight();
    float bars_width = 15.f;
    float sv_picker_size = ImMax(bars_width * 1, width - (alpha_bar ? 2 : 1) * (bars_width));
    float bar0_pos_x = bars_pos.x + 4.f;
    float bar1_pos_x = bar0_pos_x + bars_width + 10;
    ImVec2 picker_pos = ImVec2(bar1_pos_x + 25.f, window->DC.CursorPos.y);
    float bars_triangles_half_sz = IM_TRUNC(bars_width * 0.20f);

    float backup_initial_col[4];
    memcpy(backup_initial_col, col, components * sizeof(float));

    float H = col[0], S = col[1], V = col[2];
    float R = col[0], G = col[1], B = col[2];
    if (flags & ImGuiColorEditFlags_InputRGB) {
        ColorConvertRGBtoHSV(R, G, B, H, S, V);
        ColorEditRestoreHS(col, &H, &S, &V);
    }
    else if (flags & ImGuiColorEditFlags_InputHSV) {
        ColorConvertHSVtoRGB(H, S, V, R, G, B);
    }

    bool value_changed = false, value_changed_h = false, value_changed_sv = false;

    ImGui::SetCursorScreenPos(picker_pos);
    InvisibleButton("sv", ImVec2(sv_picker_size, sv_picker_size));
    if (IsItemActive()) {
        S = ImSaturate((io.MousePos.x - picker_pos.x) / (sv_picker_size - 1));
        V = 1.0f - ImSaturate((io.MousePos.y - picker_pos.y) / (sv_picker_size - 1));
        ColorEditRestoreH(col, &H);
        value_changed = value_changed_sv = true;
    }
    if (!(flags & ImGuiColorEditFlags_NoOptions)) OpenPopupOnItemClick("context", ImGuiPopupFlags_MouseButtonRight);

    SetCursorScreenPos(ImVec2(bar0_pos_x, picker_pos.y));
    InvisibleButton("hue", ImVec2(bars_width, sv_picker_size));
    if (IsItemActive()) {
        H = ImSaturate((io.MousePos.y - picker_pos.y) / (sv_picker_size - 1));
        value_changed = value_changed_h = true;
    }

    if (alpha_bar) {
        SetCursorScreenPos(ImVec2(bar1_pos_x, picker_pos.y));
        InvisibleButton("alpha", ImVec2(bars_width, sv_picker_size));
        if (IsItemActive()) {
            col[3] = 1.0f - ImSaturate((io.MousePos.y - picker_pos.y) / (sv_picker_size - 1));
            value_changed = true;
        }
    }

    if (value_changed_h || value_changed_sv) {
        if (flags & ImGuiColorEditFlags_InputRGB) {
            ColorConvertHSVtoRGB(H, S, V, col[0], col[1], col[2]);
            g.ColorEditSavedHue = H;
            g.ColorEditSavedSat = S;
            g.ColorEditSavedID = g.ColorEditCurrentID;
            g.ColorEditSavedColor = ColorConvertFloat4ToU32(ImVec4(col[0], col[1], col[2], 0));

            // Force value changed to ensure it's saved
            value_changed = true;
        }
        else if (flags & ImGuiColorEditFlags_InputHSV) {
            col[0] = H;
            col[1] = S;
            col[2] = V;

            // Force value changed to ensure it's saved
            value_changed = true;
        }
    }

    if (value_changed) {
        if (flags & ImGuiColorEditFlags_InputRGB) {
            R = col[0];
            G = col[1];
            B = col[2];
            ColorConvertRGBtoHSV(R, G, B, H, S, V);
            ColorEditRestoreHS(col, &H, &S, &V);
        }
        else if (flags & ImGuiColorEditFlags_InputHSV) {
            H = col[0];
            S = col[1];
            V = col[2];
            ColorConvertHSVtoRGB(H, S, V, R, G, B);
        }
    }

    const int style_alpha8 = IM_F32_TO_INT8_SAT(style.Alpha);
    const ImU32 col_black = IM_COL32(0, 0, 0, style_alpha8);
    const ImU32 col_white = IM_COL32(255, 255, 255, style_alpha8);
    const ImU32 col_midgrey = IM_COL32(128, 128, 128, style_alpha8);
    const ImU32 col_hues[6 + 1] = { IM_COL32(255, 0, 0, style_alpha8), IM_COL32(255, 255, 0, style_alpha8),
                                   IM_COL32(0, 255, 0, style_alpha8), IM_COL32(0, 255, 255, style_alpha8),
                                   IM_COL32(0, 0, 255, style_alpha8), IM_COL32(255, 0, 255, style_alpha8),
                                   IM_COL32(255, 0, 0, style_alpha8) };

    ImVec4 hue_color_f(1, 1, 1, style.Alpha);
    ColorConvertHSVtoRGB(H, 1, 1, hue_color_f.x, hue_color_f.y, hue_color_f.z);
    ImU32 hue_color32 = ColorConvertFloat4ToU32(hue_color_f);
    ImU32 user_col32_striped_of_alpha = ColorConvertFloat4ToU32(ImVec4(R, G, B, style.Alpha));

    static std::map<ImGuiID, picker_state> anim;
    picker_state& state = anim[ImGui::GetID(label)];

    ImVec2 sv_cursor_pos;

    draw_list->AddRectFilledMultiColor(picker_pos,
        picker_pos + ImVec2(sv_picker_size, sv_picker_size),
        col_white,
        hue_color32,
        hue_color32,
        col_white,
        5.f);
    draw_list->AddRectFilledMultiColor(picker_pos - ImVec2(1, 1),
        picker_pos + ImVec2(sv_picker_size + 1, sv_picker_size + 1),
        0,
        0,
        col_black,
        col_black,
        5.f);

    sv_cursor_pos.x = ImClamp(IM_ROUND(picker_pos.x + ImSaturate(S) * sv_picker_size),
        picker_pos.x + 2,
        picker_pos.x + sv_picker_size - 2);
    sv_cursor_pos.y = ImClamp(IM_ROUND(picker_pos.y + ImSaturate(1 - V) * sv_picker_size),
        picker_pos.y + 2,
        picker_pos.y + sv_picker_size - 2);

    for (int i = 0; i < 6; ++i)
        draw_list->AddRectFilledMultiColor(ImVec2(bar0_pos_x,
            picker_pos.y + i * (sv_picker_size / 6) - (i == 5 ? 1 : 0)),
            ImVec2(bar0_pos_x + bars_width,
                picker_pos.y + (i + 1) * (sv_picker_size / 6) + (i == 0 ? 1 : 0)),
            col_hues[i],
            col_hues[i],
            col_hues[i + 1],
            col_hues[i + 1],
            10.f,
            i == 0 ? ImDrawFlags_RoundCornersTop : i == 5
            ? ImDrawFlags_RoundCornersBottom
            : ImDrawFlags_RoundCornersNone);

    float bar0_line_y = IM_ROUND(picker_pos.y + H * sv_picker_size);
    bar0_line_y = ImClamp(bar0_line_y, picker_pos.y + 3.f, picker_pos.y + (sv_picker_size - 13));

    state.hue_bar = ImLerp(state.hue_bar, bar0_line_y + 5, g.IO.DeltaTime * 24.f);

    draw_list->AddShadowCircle(ImVec2(bar0_pos_x + 7.5f, state.hue_bar), 4.5f, col_black, 15.f, ImVec2(0, 0));
    draw_list->AddCircle(ImVec2(bar0_pos_x + 7.5f, state.hue_bar), 4.5f, col_white, 100.f, 1.5f);

    float sv_cursor_rad = value_changed_sv ? 10.0f : 6.0f;
    int sv_cursor_segments = draw_list->_CalcCircleAutoSegmentCount(sv_cursor_rad);

    state.circle_move = ImLerp(state.circle_move, sv_cursor_pos, g.IO.DeltaTime * 20.f);
    state.circle = ImLerp(state.circle, value_changed_sv ? 6.0f : 4.0f, g.IO.DeltaTime * 24.f);

    draw_list->AddCircle(state.circle_move, state.circle, col_white, sv_cursor_segments, 2.f);

    if (alpha_bar) {
        float alpha = ImSaturate(col[3]);
        ImRect bar1_bb(bar1_pos_x, picker_pos.y, bar1_pos_x + bars_width, picker_pos.y + sv_picker_size);

        ImGui::RenderColorRectWithAlphaCheckerboard(draw_list,
            bar1_bb.Min,
            bar1_bb.Max,
            ImColor(1.f, 1.f, 1.f, 0.6f),
            5.f,
            ImVec2(1, 1),
            100.f,
            ImDrawFlags_RoundCornersAll);
        draw_list->AddRectFilledMultiColor(bar1_bb.Min,
            bar1_bb.Max,
            user_col32_striped_of_alpha,
            user_col32_striped_of_alpha,
            user_col32_striped_of_alpha & ~IM_COL32_A_MASK,
            user_col32_striped_of_alpha & ~IM_COL32_A_MASK,
            100.f);

        draw_list->AddRect(bar1_bb.Min - ImVec2(2, 2), bar1_bb.Max + ImVec2(2, 2), gui.window_bg, 100.f, 0, 3.f);

        float bar1_line_y = IM_ROUND(picker_pos.y + (1.0f - alpha) * sv_picker_size);
        bar1_line_y = ImClamp(bar1_line_y, picker_pos.y + 3.f, picker_pos.y + (sv_picker_size - 13));

        state.alpha_bar = ImLerp(state.alpha_bar, bar1_line_y + 5, g.IO.DeltaTime * 24.f);
        draw_list->AddShadowCircle(ImVec2(bar1_pos_x + 7.5f, state.alpha_bar), 4.5f, col_black, 15.f, ImVec2(0, 0));
        draw_list->AddCircle(ImVec2(bar1_pos_x + 7.5f, state.alpha_bar), 4.5f, col_white, 100.f, 1.5f);
    }

    EndGroup();

    // Always mark as changed if any interaction occurred with the color picker
    // Check for any interaction with the color picker
    bool any_interaction = value_changed || value_changed_h || value_changed_sv ||
                          ImGui::IsItemActive() || ImGui::IsMouseDown(0);

    // Force the color to be marked as changed
    if (any_interaction) {
        // Force the color to be different from the backup to ensure it's saved
        if (memcmp(backup_initial_col, col, components * sizeof(float)) == 0) {
            // Add a tiny value to ensure it's different
            col[0] = ImClamp(col[0] + 0.0001f, 0.0f, 1.0f);
        }

        // Mark item as edited to ensure changes are saved
        MarkItemEdited(g.LastItemData.ID);

        // Set value_changed to true to ensure the caller knows the color changed
        value_changed = true;

        // Save the current color as the new saved color
        g.ColorEditSavedColor = ColorConvertFloat4ToU32(ImVec4(col[0], col[1], col[2], col[3]));

        // Force an immediate update of the color
        memcpy(col, col, components * sizeof(float));
    }

    // Always return true if there was any interaction to force the caller to update
    if (any_interaction)
        return true;

    PopID();

    return value_changed;
}

bool nav_elements::CheckboxComponent(const char* name, bool* v, const char* description, const char* icon, bool hasHotkey, std::vector<ColorState> colorStates, int* key, bool* mode, bool isPremium) {
    // --- Input validation to prevent crashes ---
    if (!name || !v || !description) {
        return false;
    }

    // --- Early return check ---
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems)
        return false;

    // --- Setup and ID generation ---
    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(name);
    const std::string featureName = std::string(name);

    // --- Layout calculations ---
    const ImVec2 pos = window->DC.CursorPos;
    const float content_width = GetContentRegionMax().x - style.GrabMinSize - g.Style.WindowPadding.x;
    const float component_height = 60.f;
    const float colorPickerSize = 26.f;
    const float settingsButtonSize = 26.f;
    const float elementPadding = 15.0f;

    // Calculate total width needed for color pickers and settings button
    float leftSideWidth = 0;
    leftSideWidth += colorStates.size() * (colorPickerSize + elementPadding);
    if (hasHotkey) leftSideWidth += settingsButtonSize + elementPadding;
    if (leftSideWidth > 0) leftSideWidth -= elementPadding; // Remove last spacing

    const ImVec2 total_size(content_width, component_height);
    const ImRect total_bb(pos, ImVec2(pos.x + total_size.x, pos.y + total_size.y));
    ItemSize(total_bb, 0.f);

    if (!ItemAdd(total_bb, id))
        return false;

    // --- Checkbox dimensions and positioning ---
    const ImVec2 check_size = ImVec2(50, 26);
    const float check_spacing = (total_bb.GetHeight() - check_size.y) / 2;
    const ImRect check_bb(
        ImVec2(total_bb.Max.x - check_spacing - check_size.x, total_bb.Max.y - check_spacing - check_size.y),
        ImVec2(total_bb.Max.x - check_spacing, total_bb.Max.y - check_spacing)
    );

    // --- Input handling ---
    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(total_bb, id, &hovered, &held);
    bool settingsButtonClicked = false;

    // --- Hotkey integration using new system ---
    if (hasHotkey && key != nullptr && v != nullptr) 
    {
        if (InputManager::IsKeyDown(*key)) 
        
        {
            if (mode && *mode) { // Toggle mode
                static std::map<std::string, bool> keyPressed;
                if (InputManager::IsKeyPressed(*key)) {
                    *v = !(*v);
                }
            }
            else { // Hold mode
                *v = true;
            }
        }
        else if (mode && !(*mode)) { // Handle hold mode release
            *v = false;
        }
    }

    // --- Text positioning ---
    const ImVec2 description_size = CalcTextSize(description, NULL, true);
    const ImVec2 label_size = CalcTextSize(name, NULL, true);
    const ImVec2 label_pos = ImVec2(
        total_bb.Min.x + 60.f + (total_bb.GetSize().y - (label_size.y + description_size.y)) / 2,
        total_bb.GetCenter().y - (label_size.y + description_size.y) / 2
    );

    // --- Animation state management ---
    static std::map<ImGuiID, check_state> anim;
    auto it_anim = anim.emplace(id, check_state()).first;

    static std::map<ImGuiID, edit_state> animation;
    edit_state& state = animation[id];

    // Update animation colors
    const float anim_speed = GetAnimSpeed();
    const bool is_active = *v;

    // Consolidated color animations
    it_anim->second.check_color = ImLerp(it_anim->second.check_color,
        is_active ? gui.checkboxstroke : ImColor(34, 27, 27, 255), anim_speed / 3);
    it_anim->second.check_color_circle = ImLerp(it_anim->second.check_color_circle,
        is_active ? gui.main : ImColor(64, 57, 57, 255), anim_speed / 3);
    it_anim->second.rect_color = ImLerp(it_anim->second.rect_color,
        is_active || hovered ? gui.main : func.GetColorWithAlpha(gui.main, 0.3f), anim_speed);
    it_anim->second.rect_shadow_color = ImLerp(it_anim->second.rect_shadow_color,
        is_active || hovered ? gui.main : func.GetColorWithAlpha(ImColor(0, 0, 0, 255), 0.3f), anim_speed);
    it_anim->second.check_rect_color = ImLerp(it_anim->second.check_rect_color,
        is_active ? gui.checkboxstrokeactive : gui.checkboxstroke, anim_speed / 3);
    it_anim->second.circle_alpha = ImLerp(it_anim->second.circle_alpha,
        is_active ? 0.9f : 0.6f, anim_speed);
    it_anim->second.icon_color = ImLerp(it_anim->second.icon_color,
        is_active || hovered ? func.GetDarkColor(gui.main) : gui.main, anim_speed);
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity,
        (is_active ? 0.08f : hovered ? 0.04f : 0.2f), 0.14f * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.background_color = ImLerp(it_anim->second.background_color,
        is_active || hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f), anim_speed);
    it_anim->second.background_rect_color = ImLerp(it_anim->second.background_rect_color,
        hovered ? gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f), anim_speed);

    // Handle circle animation
    EasingAnimationV2("circle_offset" + featureName, &it_anim->second.circle_offset,
        is_active ? ImVec2(24, 0) : ImVec2(0, 0), 0.25f, imanim::EasingCurve::Type::InOutCubic, -1);

    // --- Premium System Integration ---
    PremiumSystem& premiumSystem = PremiumSystem::getInstance();
    bool isFeaturePremium = isPremium || premiumSystem.isFeaturePremium(featureName);
    bool isUserPremium = premiumSystem.isPremiumUser();
    bool shouldLock = isFeaturePremium && !isUserPremium;

    // Premium animation state management
    static std::map<ImGuiID, premium_state> premium_anim;
    auto it_premium = premium_anim.emplace(id, premium_state()).first;
    
    // Update premium animations
    it_premium->second.time += ImGui::GetIO().DeltaTime;
    
    // Lock overlay animation
    it_premium->second.lockOverlayOpacity = ImLerp(it_premium->second.lockOverlayOpacity,
        shouldLock ? 0.8f : 0.0f, anim_speed);
    
    // Premium badge animation (for premium users)
    it_premium->second.premiumBadgeOpacity = ImLerp(it_premium->second.premiumBadgeOpacity,
        (isFeaturePremium && isUserPremium) ? 1.0f : 0.0f, anim_speed);
    
    // Upgrade button animation (for non-premium users)
    it_premium->second.upgradeButtonOpacity = ImLerp(it_premium->second.upgradeButtonOpacity,
        shouldLock ? 1.0f : 0.0f, anim_speed);
    
    // Premium glow effect
    it_premium->second.premiumGlow = ImLerp(it_premium->second.premiumGlow,
        (isFeaturePremium && isUserPremium) ? 1.0f : 0.0f, anim_speed);
    
    // Premium pulse animation
    it_premium->second.premiumPulse = sinf(it_premium->second.time * 2.0f) * 0.5f + 0.5f;
    
    // Premium badge scale animation
    float targetScale = (isFeaturePremium && isUserPremium) ? (1.0f + it_premium->second.premiumPulse * 0.1f) : 1.0f;
    it_premium->second.premiumBadgeScale = ImLerp(it_premium->second.premiumBadgeScale, targetScale, anim_speed * 2);
    
    // Upgrade button scale animation (hover effect)
    bool upgradeButtonHovered = false; // Will be set in the upgrade button rendering section
    it_premium->second.upgradeButtonScale = ImLerp(it_premium->second.upgradeButtonScale,
        upgradeButtonHovered ? 1.05f : 1.0f, anim_speed * 3);

    // Prevent feature activation if locked
    if (shouldLock && pressed && !settingsButtonClicked) {
        pressed = false; // Block the feature from being activated
        *v = false; // Force the feature to remain off
    }

    // --- Rendering ---
    // Background
    window->DrawList->AddRectFilled(total_bb.Min, total_bb.Max, 
        GetColorU32(it_anim->second.background_color), 3.0f);

    // Border
    window->DrawList->AddRect(total_bb.Min, total_bb.Max, 
        GetColorU32(it_anim->second.background_rect_color), 3.0f);

    // Icon
    /*PushFont(iconsBig);
    window->DrawList->AddText(
        ImVec2(total_bb.Min.x + total_bb.GetSize().y / 2 - CalcTextSize(ICON_CIRCLE_FILL).x / 2, 
               total_bb.Min.y + total_bb.GetSize().y / 2 - CalcTextSize(ICON_CIRCLE_FILL).y / 2),
        GetColorU32(it_anim->second.icon_color), ICON_CIRCLE_FILL);
    PopFont();*/

    // Text
    window->DrawList->AddText(label_pos, gui.text[0], name);
    window->DrawList->AddText(ImVec2(label_pos.x, label_pos.y + label_size.y + 2), gui.text[1], description);

    // Left background rectangle with shadow
    GetWindowDrawList()->AddRectFilled(total_bb.Min,
        ImVec2(total_bb.Min.x + total_bb.GetSize().y, total_bb.Min.y + total_bb.GetSize().y),
        GetColorU32(it_anim->second.rect_color), style.FrameRounding, ImDrawFlags_RoundCornersLeft);

    window->DrawList->AddShadowRect(total_bb.Min,
        ImVec2(total_bb.Min.x + total_bb.GetSize().y - 5, total_bb.Max.y),
        GetColorU32(it_anim->second.rect_shadow_color), 20.f, ImVec2(0, 0), 0, ImDrawFlags_RoundCornersLeft);

    // Checkbox background and border
    GetWindowDrawList()->AddRectFilled(check_bb.Min, check_bb.Max, 
        GetColorU32(it_anim->second.check_color), 30.f);
    GetWindowDrawList()->AddRect(check_bb.Min, check_bb.Max, 
        GetColorU32(it_anim->second.check_rect_color), 30.f);

    // Checkbox shadow and circle
    window->DrawList->AddShadowCircle(
        ImVec2((check_bb.Min.x + check_bb.Max.x) / 2, (check_bb.Min.y + check_bb.Max.y) / 2),
        8.f, GetColorU32(it_anim->second.check_color), 15.f, ImVec2(0, 0), 0, 360);

    // Circle shadow and icon shadow
    window->DrawList->AddShadowCircle(
        ImVec2(check_bb.Min.x + 13 + it_anim->second.circle_offset.x, check_bb.Min.y + 13),
        6.f, GetColorU32(it_anim->second.check_color_circle), 70.f, ImVec2(0, 0), 0, 360);
    window->DrawList->AddShadowCircle(
        ImVec2(total_bb.Min.x + total_bb.GetSize().y / 2, total_bb.Min.y + total_bb.GetSize().y / 2),
        9.f, GetColorU32(it_anim->second.icon_color), 40, ImVec2(0, 0), 0, 360);

    // Slider circle
    window->DrawList->AddCircleFilled(
        ImVec2(check_bb.Min.x + 13 + it_anim->second.circle_offset.x, check_bb.Min.y + 13),
        8.f, GetColorU32(it_anim->second.check_color_circle), 360);

    // --- Custom icon rendering for description ---
    // Check if description contains an icon after '^' character
    if (icon != nullptr) {
        PushFont(iconsBig);
        window->DrawList->AddText(
            func.CalcTextPos(total_bb.Min,
                ImVec2(total_bb.Min.x + total_bb.GetSize().y, total_bb.Min.y + total_bb.GetSize().y),
                icon),
            GetColorU32(it_anim->second.icon_color),
            icon);
        PopFont();
    }

    // --- Settings expansion handling ---
    // Access global state for tracking which features are expanded
    static std::map<std::string, int> activeColorPickerIndexMap;
    
    // Ensure the feature exists in maps before creating references
    if (g_expandedFeaturesMap.find(featureName) == g_expandedFeaturesMap.end()) {
        g_expandedFeaturesMap[featureName] = false;
    }
    if (g_expansionAnimationState.find(featureName) == g_expansionAnimationState.end()) {
        g_expansionAnimationState[featureName] = 0.0f;
        }

    auto& isExpanded = g_expandedFeaturesMap[featureName];
    auto& expansionAnimState = g_expansionAnimationState[featureName];
    auto& activeColorPickerIndex = activeColorPickerIndexMap[featureName];

    if (activeColorPickerIndex < 0) activeColorPickerIndex = -1;

    // Update expansion animation with improved easing and faster response
    float targetState = isExpanded ? 1.0f : 0.0f;
    float animSpeed = GetAnimSpeed() * 2.0f; // Increased speed significantly for better responsiveness
    
    // Use smooth easing with clamping to prevent overshoot
    float difference = targetState - expansionAnimState;
    expansionAnimState += difference * animSpeed;

    // Clamp values to prevent animation artifacts
    expansionAnimState = (std::max)(0.0f, (std::min)(1.0f, expansionAnimState));
    
    // More aggressive snap to final state to prevent freezing and micro-animations
    if (fabs(difference) < 0.02f) {
        expansionAnimState = targetState;
            }

    // Store the expansion state globally for access from GUI.cpp
    g_expansionAnimationState[featureName] = expansionAnimState;

    // --- Settings button rendering ---
    if ((hasHotkey && key != nullptr && mode != nullptr) || !colorStates.empty()) {
        float currentX = total_bb.Max.x - check_size.x - elementPadding - (settingsButtonSize + elementPadding);
        ImVec2 settingsButtonPos = ImVec2(currentX, total_bb.Min.y + (total_bb.GetSize().y - settingsButtonSize) / 2);
        ImRect settingsButtonRect(settingsButtonPos, 
            ImVec2(settingsButtonPos.x + settingsButtonSize, settingsButtonPos.y + settingsButtonSize));

        bool settingsHovered = ImGui::IsMouseHoveringRect(settingsButtonRect.Min, settingsButtonRect.Max);

        // Draw settings button
        window->DrawList->AddRectFilled(settingsButtonRect.Min, settingsButtonRect.Max,
            GetColorU32(it_anim->second.check_color), 3.0f);
        window->DrawList->AddRect(settingsButtonRect.Min, settingsButtonRect.Max,
            GetColorU32(it_anim->second.check_rect_color), 3.0f);

        // Settings icon - expand/collapse
        PushFont(iconsSmall);
        const char* icon = isExpanded ? ICON_MS_EXPAND_LESS : ICON_MS_EXPAND_MORE;
        ImVec2 iconSize = CalcTextSize(icon);
        ImVec2 buttonCenter = ImVec2(settingsButtonRect.Min.x + settingsButtonSize / 2,
                                   settingsButtonRect.Min.y + settingsButtonSize / 2);
        ImVec2 iconPos = ImVec2(buttonCenter.x - (iconSize.x / 2), buttonCenter.y - (iconSize.y / 2) - 6.5f);

        window->DrawList->AddText(iconPos, gui.main, icon);
        PopFont();

        // Handle settings button click - toggle expansion
        if (settingsHovered && ImGui::IsMouseClicked(0)) {
            // Prevent rapid clicking during animation
            static std::map<std::string, float> lastClickTime;
            float currentTime = ImGui::GetTime();
            
            if (lastClickTime.find(featureName) == lastClickTime.end() || 
                (currentTime - lastClickTime[featureName]) > 0.1f) { // 100ms debounce
                
                // Close all other expanded features first
                for (auto& pair : g_expandedFeaturesMap) {
                    if (pair.first != featureName) {
                        pair.second = false;
                    }
                }
                
                // Toggle current feature
                isExpanded = !isExpanded;
                lastClickTime[featureName] = currentTime;
            }
            
            settingsButtonClicked = true;
            pressed = false;
        }

        // Settings tooltip
        if (settingsHovered && !ImGui::IsMouseClicked(0) && !gui.darkoverlay) {
            ImGui::BeginTooltip();
            ImGui::Text(isExpanded ? "Hide Settings" : "Show Settings");
            ImGui::EndTooltip();
        }
    }

    // --- Premium UI Elements ---
    // Premium badge (for premium users with premium features)
    if (it_premium->second.premiumBadgeOpacity > 0.01f) {
        const float badgeSize = 20.0f;
        const float badgePadding = 5.0f;
        
        ImVec2 badgePos = ImVec2(
            total_bb.Max.x - badgeSize - badgePadding,
            total_bb.Min.y + badgePadding
        );
        
        ImVec2 badgeCenter = ImVec2(badgePos.x + badgeSize / 2, badgePos.y + badgeSize / 2);
        
        // Premium badge background with glow
        ImColor premiumColor = ImColor(PremiumColors::PREMIUM_GOLD.x, PremiumColors::PREMIUM_GOLD.y, 
                                     PremiumColors::PREMIUM_GOLD.z, it_premium->second.premiumBadgeOpacity);
        
        // Glow effect
        window->DrawList->AddShadowCircle(badgeCenter, badgeSize / 2 * it_premium->second.premiumBadgeScale,
            premiumColor, 15.0f * it_premium->second.premiumGlow, ImVec2(0, 0), 0, 360);
        
        // Badge circle
        window->DrawList->AddCircleFilled(badgeCenter, (badgeSize / 2 - 2) * it_premium->second.premiumBadgeScale,
            premiumColor, 32);
        
        // Badge border
        window->DrawList->AddCircle(badgeCenter, (badgeSize / 2 - 2) * it_premium->second.premiumBadgeScale,
            ImColor(PremiumColors::PREMIUM_GOLD_LIGHT.x, PremiumColors::PREMIUM_GOLD_LIGHT.y, 
                   PremiumColors::PREMIUM_GOLD_LIGHT.z, it_premium->second.premiumBadgeOpacity), 32, 1.5f);
        
        // "PRO" text
        PushFont(iconsSmall);
        const char* proText = "PRO";
        ImVec2 textSize = CalcTextSize(proText);
        ImVec2 textPos = ImVec2(badgeCenter.x - textSize.x / 2, badgeCenter.y - textSize.y / 2);
        
        window->DrawList->AddText(textPos, 
            ImColor(0.0f, 0.0f, 0.0f, it_premium->second.premiumBadgeOpacity), proText);
        PopFont();
    }
    
    // Lock overlay (for non-premium users with premium features) - Updated to use gui.main
    if (it_premium->second.lockOverlayOpacity > 0.01f) {
        // Lock overlay background using gui.main with transparency
        ImColor overlayColor = func.GetColorWithAlpha(gui.main, it_premium->second.lockOverlayOpacity * 0.3f);
        window->DrawList->AddRectFilled(total_bb.Min, total_bb.Max, overlayColor, 3.0f);
    }
    
    // Enhanced animated stars with consistent count and smooth water-like movement
    if (isFeaturePremium) {
        // Initialize stars if not already done - consistent 20 stars for all components
        if (it_premium->second.stars.empty()) {
            const int STAR_COUNT = 20; // Consistent star count across all components
            
            for (int i = 0; i < STAR_COUNT; i++) {
                premium_state::StarData star;
                
                // Calculate component dimensions for proper distribution
                float componentWidth = total_bb.GetWidth();
                float componentHeight = total_bb.GetHeight();
                
                // Create a grid-based distribution for consistent coverage
                int gridX = i % 5; // 5 columns
                int gridY = i / 5; // 4 rows
                
                // Calculate normalized position within the component
                float normalizedX = (float)gridX / 4.0f; // 0.0 to 1.0
                float normalizedY = (float)gridY / 3.0f; // 0.0 to 1.0
                
                // Add some randomness to avoid perfect grid alignment
                float randomOffsetX = ((float)(rand() % 100) / 100.0f - 0.5f) * 0.3f;
                float randomOffsetY = ((float)(rand() % 100) / 100.0f - 0.5f) * 0.3f;
                
                // Calculate final position relative to component center
                float posX = (normalizedX + randomOffsetX - 0.5f) * componentWidth * 0.8f;
                float posY = (normalizedY + randomOffsetY - 0.5f) * componentHeight * 0.8f;
                
                star.position = ImVec2(posX, posY);
                star.phase = (float)i / STAR_COUNT * 2.0f * IM_PI; // Distributed phases for smooth animation
                star.size = 2.0f + ((float)(rand() % 60) / 100.0f) * 3.0f; // 2.0-5.0px for variety
                star.opacity = 0.4f + ((float)(rand() % 60) / 100.0f) * 0.5f; // 0.4-0.9 opacity
                it_premium->second.stars.push_back(star);
            }
        }
        
        // Update star animation time with water-like smoothness
        it_premium->second.starAnimationTime += ImGui::GetIO().DeltaTime * 0.8f; // Slower, smoother movement
        
        // Draw animated stars with water-like movement
        ImVec2 componentCenter = total_bb.GetCenter();
        for (auto& star : it_premium->second.stars) {
            // Water-like smooth movement using multiple sine waves
            float animPhase = it_premium->second.starAnimationTime + star.phase;
            
            // Create smooth, flowing motion like water
            float waterFlowX = sin(animPhase * 0.7f) * 3.0f + 
                              sin(animPhase * 0.3f + star.phase * 0.5f) * 2.0f +
                              cos(animPhase * 0.5f) * 1.5f;
            
            float waterFlowY = cos(animPhase * 0.6f) * 2.5f + 
                              sin(animPhase * 0.4f + star.phase * 0.3f) * 1.8f +
                              cos(animPhase * 0.8f) * 1.2f;
            
            ImVec2 animatedOffset = ImVec2(waterFlowX, waterFlowY);
            
            ImVec2 starPos = ImVec2(
                componentCenter.x + star.position.x + animatedOffset.x,
                componentCenter.y + star.position.y + animatedOffset.y
            );
            
            // Smooth water-like pulsing with gentle waves
            float waterPulse1 = sin(animPhase * 1.2f) * 0.4f + 0.6f; // Gentle base pulse
            float waterPulse2 = cos(animPhase * 0.6f + star.phase) * 0.2f + 0.8f; // Secondary wave
            float waterPulse3 = sin(animPhase * 0.9f) * 0.15f + 0.85f; // Tertiary wave
            float finalOpacity = star.opacity * waterPulse1 * waterPulse2 * waterPulse3;
            
            // Draw star using gui.main color with water-like transparency
            ImColor starColor = func.GetColorWithAlpha(gui.main, finalOpacity);
            
            // Draw smooth 8-pointed star shape
            float halfSize = star.size * 0.5f;
            float quarterSize = star.size * 0.25f;
            
            // Main cross (primary rays) - thinner for water-like appearance
            window->DrawList->AddLine(
                ImVec2(starPos.x - halfSize, starPos.y),
                ImVec2(starPos.x + halfSize, starPos.y),
                starColor, 1.8f
            );
            window->DrawList->AddLine(
                ImVec2(starPos.x, starPos.y - halfSize),
                ImVec2(starPos.x, starPos.y + halfSize),
                starColor, 1.8f
            );
            
            // Diagonal cross (secondary rays) - even thinner
            window->DrawList->AddLine(
                ImVec2(starPos.x - quarterSize, starPos.y - quarterSize),
                ImVec2(starPos.x + quarterSize, starPos.y + quarterSize),
                starColor, 1.2f
            );
            window->DrawList->AddLine(
                ImVec2(starPos.x - quarterSize, starPos.y + quarterSize),
                ImVec2(starPos.x + quarterSize, starPos.y - quarterSize),
                starColor, 1.2f
            );
            
            // Add gentle water-like glow effect
            if (finalOpacity > 0.25f) {
                ImColor glowColor = func.GetColorWithAlpha(gui.main, finalOpacity * 0.3f);
                window->DrawList->AddShadowCircle(starPos, star.size * 1.5f, glowColor, 12.0f, ImVec2(0, 0), 0, 360);
            }
            
            // Add subtle sparkle effect for water-like shimmer
            if (finalOpacity > 0.7f) {
                ImColor sparkleColor = func.GetColorWithAlpha(gui.main, finalOpacity * 0.4f);
                window->DrawList->AddCircleFilled(starPos, 0.8f, sparkleColor, 6);
            }
        }
    }
    
    // Upgrade button (for non-premium users with premium features) - Redesigned with gui.main
    if (it_premium->second.upgradeButtonOpacity > 0.01f) {
        const char* buttonText = "Upgrade to Premium";
        ImVec2 buttonTextSize = CalcTextSize(buttonText);
        const float buttonPadding = 12.0f;
        const float buttonHeight = 32.0f;
        
        // Calculate button width based on text content with padding
        const float buttonWidth = buttonTextSize.x + (buttonPadding * 2);
        
        ImVec2 buttonPos = ImVec2(
            total_bb.GetCenter().x - buttonWidth / 2,
            total_bb.Max.y - buttonHeight - 8.0f
        );
        
        ImRect upgradeButtonRect(buttonPos, ImVec2(buttonPos.x + buttonWidth, buttonPos.y + buttonHeight));
        
        // Check if upgrade button is hovered
        upgradeButtonHovered = ImGui::IsMouseHoveringRect(upgradeButtonRect.Min, upgradeButtonRect.Max);
        
        // Update scale based on hover
        it_premium->second.upgradeButtonScale = ImLerp(it_premium->second.upgradeButtonScale,
            upgradeButtonHovered ? 1.02f : 1.0f, anim_speed * 3);
        
        // Scale the button
        ImVec2 scaledSize = ImVec2(buttonWidth * it_premium->second.upgradeButtonScale,
                                  buttonHeight * it_premium->second.upgradeButtonScale);
        ImVec2 scaledPos = ImVec2(
            total_bb.GetCenter().x - scaledSize.x / 2,
            total_bb.Max.y - scaledSize.y - 8.0f
        );
        ImRect scaledButtonRect(scaledPos, ImVec2(scaledPos.x + scaledSize.x, scaledPos.y + scaledSize.y));
        
        // Button background using gui.main with transparency
        ImColor buttonBackgroundColor = func.GetColorWithAlpha(gui.main,
            it_premium->second.upgradeButtonOpacity * (upgradeButtonHovered ? 0.2f : 0.15f));
        window->DrawList->AddRectFilled(scaledButtonRect.Min, scaledButtonRect.Max, buttonBackgroundColor, 6.0f);
        
        // Grey rounded border
        ImColor borderColor = ImColor(44, 37, 37, (int)(it_premium->second.upgradeButtonOpacity * 255 * 0.8f));
        window->DrawList->AddRect(scaledButtonRect.Min, scaledButtonRect.Max, borderColor, 6.0f, 0, 1.f);
        
        // Button text with gui.main color
        ImColor textColor = func.GetColorWithAlpha(gui.main, it_premium->second.upgradeButtonOpacity);
        ImVec2 buttonTextPos = ImVec2(
            scaledButtonRect.GetCenter().x - buttonTextSize.x / 2,
            scaledButtonRect.GetCenter().y - buttonTextSize.y / 2
        );
        
        window->DrawList->AddText(buttonTextPos, textColor, buttonText);
        
        // Handle upgrade button click
        if (upgradeButtonHovered && ImGui::IsMouseClicked(0)) {
            premiumSystem.onUpgradeButtonClick();
            pressed = false; // Prevent checkbox from toggling
        }
        
        // Subtle glow effect on hover using gui.main
        if (upgradeButtonHovered) {
            ImColor glowColor = func.GetColorWithAlpha(gui.main, it_premium->second.upgradeButtonOpacity * 0.3f);
            window->DrawList->AddShadowRect(scaledButtonRect.Min, scaledButtonRect.Max,
                glowColor, 8.0f, ImVec2(0, 0), 0, 6.0f);
        }
    }

    // --- Checkbox toggle handling ---
    if (ImGui::IsItemClicked() && !settingsButtonClicked && v != nullptr && !shouldLock) {
        bool newState = !(*v);
        *v = newState;
        MarkItemEdited(id);

        // Simple settings save for checkbox changes
        SettingsHelper::SaveSettings();
    }

    // Reserve space for expanded content but don't render it here
    // The actual content will be rendered directly in GUI.cpp after this component
    // We don't add spacing here since RenderExpandedContentAnimated handles all spacing

    return pressed;
}


bool nav_elements::ColorButton(const char* desc_id, const ImVec4& col, ImGuiColorEditFlags flags, const ImVec2& size_arg) {
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *ImGui::GetCurrentContext();
    const ImGuiID id = window->GetID(desc_id);

    // Use the provided size or default to 20x20
    const ImVec2 size = size_arg.x <= 0 || size_arg.y <= 0 ? ImVec2(20, 20) : size_arg;
    const ImRect bb(window->DC.CursorPos, ImVec2(window->DC.CursorPos.x + size.x, window->DC.CursorPos.y + size.y));

    const ImGuiStyle& style = g.Style;

    ImGui::ItemSize(bb);
    if (!ImGui::ItemAdd(bb, id))
        return false;

    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(bb, id, &hovered, &held);

    ImVec4 col_rgb = col;
    if (flags & ImGuiColorEditFlags_InputHSV)
        ImGui::ColorConvertHSVtoRGB(col_rgb.x, col_rgb.y, col_rgb.z, col_rgb.x, col_rgb.y, col_rgb.z);

    static std::map<ImGuiID, check_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, check_state() });
        it_anim = anim.find(id);
    }

    // Update animation colors
    const float anim_speed = GetAnimSpeed();

    it_anim->second.check_color = ImLerp(
        it_anim->second.check_color,
        hovered ? gui.checkboxstroke : ImColor(34, 27, 27, 255),
        anim_speed / 3
    );

    it_anim->second.check_rect_color = ImLerp(
        it_anim->second.check_rect_color,
        hovered ? gui.checkboxstrokeactive : gui.checkboxstroke,
        anim_speed / 3
    );

    // Draw square background with rounded borders for the color picker
    const float rounding = 5.0f; // Rounded corners radius

    // Draw background
    window->DrawList->AddRectFilled(
        bb.Min,
        bb.Max,
        GetColorU32(it_anim->second.check_color),
        rounding
    );

    // Draw border
    window->DrawList->AddRect(
        bb.Min,
        bb.Max,
        GetColorU32(it_anim->second.check_rect_color),
        rounding,
        0,
        1.5f
    );

    // Draw inner color square with the selected color
    window->DrawList->AddRectFilled(
        ImVec2(bb.Min.x + 4.0f, bb.Min.y + 4.0f),
        ImVec2(bb.Max.x - 4.0f, bb.Max.y - 4.0f),
        ImGui::ColorConvertFloat4ToU32(col_rgb),
        rounding - 1.0f
    );

    // Add shadow for the color square with reduced intensity
    window->DrawList->AddShadowRect(
        ImVec2(bb.Min.x + 4.0f, bb.Min.y + 4.0f),
        ImVec2(bb.Max.x - 4.0f, bb.Max.y - 4.0f),
        ImGui::ColorConvertFloat4ToU32(col_rgb),
        30.f,  // Reduced shadow intensity from 70 to 30
        ImVec2(0, 0),
        0,
        rounding - 1.0f
    );

    // Add tooltip if not disabled - ensure no picker state is triggered
    if (!(flags & ImGuiColorEditFlags_NoTooltip) && hovered) {
        // Use simple tooltip to avoid any picker state changes
        // Explicitly ensure no picker state is set on hover over color buttons
        ImGui::SetTooltip("Color: %.2f, %.2f, %.2f, %.2f", col.x, col.y, col.z, col.w);

        // Explicitly ensure no picker state is set on hover
        // gui.picker_active should only be set when actually opening a color picker popup
        // Never set gui.picker_active on hover to prevent unwanted dark overlays
    }

    return pressed;
}


struct button_state {
    ImVec4 background, text;
};

bool nav_elements::Button(const char* label, const ImVec2& size_arg, ImColor color) {
    ImGuiWindow* window = GetCurrentWindow();

    if (window->SkipItems) return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(label);
    const ImVec2 label_size = CalcTextSize(label, NULL, true), pos = window->DC.CursorPos;

    ImVec2 size = CalcItemSize(size_arg, label_size.x, label_size.y);

    const ImRect bb(pos, ImVec2(pos.x + size.x, pos.y + size.y));

    ItemSize(size, 0.f);
    if (!ItemAdd(bb, id)) return false;

    bool hovered, held, pressed = ButtonBehavior(bb, id, &hovered, &held, NULL);

    static std::map<ImGuiID, button_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, button_state() });
        it_anim = anim.find(id);
    }

    bool color_changed = color != func.ImColorToImVec4(gui.main);

    it_anim->second.text = ImLerp(it_anim->second.text,
        IsItemActive() || hovered && color_changed ? color : !color_changed ? gui.text[0] : color_changed ? func.GetColorWithAlpha(color, 0.7f) : gui.text[1],
        g.IO.DeltaTime * 6.f);

    const float anim_speed = GetAnimSpeed();

    it_anim->second.background = ImLerp(
        it_anim->second.background,
        hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f),
        anim_speed
    );
    window->DrawList->AddRectFilled(bb.Min, bb.Max, GetColorU32(it_anim->second.background), 3.0f);


    GetWindowDrawList()->AddRectFilled(bb.Min, bb.Max, GetColorU32(it_anim->second.background), c::page::rounding);

    PushClipRect(bb.Min, bb.Max, true);

    GetWindowDrawList()->AddText(ImVec2(bb.Min.x + (size_arg.x - CalcTextSize(label).x) / 2, bb.Max.y - CalcTextSize(label).y - (size.y - CalcTextSize(label).y) / 2), GetColorU32(it_anim->second.text), label);

    PopClipRect();

    return pressed;
}

// Button animation state structure matching tab design
struct ButtonAnimState {
    float element_opacity = 0.0f;
    float text_opacity = 0.3f;
    ImVec4 icon_color = ImVec4(1.0f, 1.0f, 1.0f, 0.7f);
};

// Map to store button animation states
static std::map<ImGuiID, ButtonAnimState> button_anim_map;

// Custom button function with icon and animation
bool nav_elements::IconButton(const char* label, const char* icon, const ImVec2& size_arg, ImColor color, bool selected) {
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(label);

    // Calculate icon size with proper font - use smaller icons for better proportions
    ImGui::PushFont(iconsBig);
    const ImVec2 icon_size = ImGui::CalcTextSize(icon);
    ImGui::PopFont();

    // Scale up icon size for better visibility in IconButton
    const float icon_scale = 1.1f; // Make icons bigger for download/load buttons
    const ImVec2 scaled_icon_size = ImVec2(icon_size.x * icon_scale, icon_size.y * icon_scale);

    ImVec2 text_size(0, 0);
    bool has_text = label && strlen(label) > 0;
    if (has_text) {
        text_size = ImGui::CalcTextSize(label);
    }

    // Calculate content dimensions with improved spacing
    float spacing = (scaled_icon_size.x > 0 && has_text) ? style.ItemInnerSpacing.x + 8.0f : 0.0f; // Increased spacing between icon and text
    ImVec2 content_size(
        scaled_icon_size.x + text_size.x + spacing,
        ImMax(scaled_icon_size.y, text_size.y)
    );

    // Calculate final size with consistent padding
    ImVec2 size = ImGui::CalcItemSize(size_arg,
        content_size.x + style.FramePadding.x * 2.0f + 12.0f, // Added extra padding
        content_size.y + style.FramePadding.y * 2.0f + 8.0f   // Added extra padding
    );

    const ImVec2 pos = window->DC.CursorPos;
    const ImRect rect(pos, ImVec2(pos.x + size.x, pos.y + size.y));

    ImGui::ItemSize(rect, style.FramePadding.y);
    if (!ImGui::ItemAdd(rect, id))
        return false;

    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(rect, id, &hovered, &held, NULL);

    if (hovered || selected) {
        ImGui::SetMouseCursor(ImGuiMouseCursor_Hand);
    }

    // Animation state
    auto it_anim = button_anim_map.find(id);
    if (it_anim == button_anim_map.end()) {
        button_anim_map.insert({ id, ButtonAnimState() });
        it_anim = button_anim_map.find(id);
    }

    // Animation system matching tab design exactly
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity,
        (selected ? 0.04f : hovered ? 0.01f : 0.0f),
        0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    it_anim->second.text_opacity = ImLerp(it_anim->second.text_opacity,
        (selected ? 1.0f : hovered ? 0.5f : 0.3f),
        0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Icon color animation matching tab design
    it_anim->second.icon_color = ImLerp(
        it_anim->second.icon_color,
        selected || hovered ? gui.main : ImColor(1.0f, 1.0f, 1.0f, 0.7f),
        GetAnimSpeed()
    );

    // Draw background rectangle exactly like tabs
    window->DrawList->AddRectFilled(rect.Min, rect.Max,
        ImColor(1.0f, 1.0f, 1.0f, it_anim->second.element_opacity), 3.0f);

    // Draw border exactly like tabs
    ImVec4 border_color = selected || hovered ?
        ImVec4(ImColor(255, 255, 255, 10)) :
        ImVec4(ImColor(255, 255, 255, 10));
    window->DrawList->AddRect(rect.Min, rect.Max,
        ImGui::ColorConvertFloat4ToU32(border_color), 3.0f, 0, 1.0f);

    // Calculate positions for icon and text with improved centering
    ImVec2 icon_pos(0, 0);
    ImVec2 text_pos(0, 0);

    if (has_text) {
        // Position icon on the left with perfect vertical centering
        const float icon_padding = 6.0f; // Reduced padding for better fit
        icon_pos = ImVec2(
            rect.Min.x + icon_padding,
            rect.GetCenter().y - (scaled_icon_size.y * 0.5f) // Perfect vertical center
        );

        // Position text in center
        text_pos = ImVec2(
            rect.GetCenter().x - text_size.x * 0.5f,
            rect.GetCenter().y - text_size.y * 0.5f
        );
    } else {
        // Center icon alone with perfect positioning
        icon_pos = ImVec2(
            rect.GetCenter().x - scaled_icon_size.x * 0.5f,
            rect.GetCenter().y - scaled_icon_size.y * 0.5f // Perfect vertical center
        );
    }

    // Draw icon shadow with scaled size
    if (icon && *icon) {
        ImVec2 icon_center = ImVec2(
            icon_pos.x + scaled_icon_size.x * 0.5f,
            icon_pos.y + scaled_icon_size.y * 0.5f
        );

        window->DrawList->AddShadowCircle(
            icon_center,
            5.f, // Further reduced shadow size for better proportions
            ImGui::GetColorU32(it_anim->second.icon_color),
            20, // Further reduced shadow spread
            ImVec2(0, 0),
            0,
            360
        );
    }

    // Draw icon with scaled size
    if (icon && *icon) {
        ImGui::PushFont(iconsBig);
        // Scale the icon by adjusting the font size
        float original_font_size = ImGui::GetFontSize();
        ImGui::SetWindowFontScale(icon_scale);
        window->DrawList->AddText(
            icon_pos,
            ImGui::GetColorU32(it_anim->second.icon_color),
            icon
        );
        ImGui::SetWindowFontScale(1.0f); // Reset font scale
        ImGui::PopFont();
    }

    // Draw text
    if (has_text) {
        window->DrawList->AddText(
            text_pos,
            ImColor(1.0f, 1.0f, 1.0f, it_anim->second.text_opacity),
            label
        );
    }

    return pressed;
}
// Icon-only button variant
bool nav_elements::IconOnlyButton(const char* icon, const ImVec2& size_arg, ImColor color, bool selected) {
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(icon);

    // Calculate icon size with proper font - use smaller icons for icon-only buttons
    ImGui::PushFont(iconsBig);
    const ImVec2 icon_size_full = ImGui::CalcTextSize(icon);
    ImGui::PopFont();

    // Scale down icon size for better proportions in icon-only buttons
    const float icon_scale = 0.8f; // Make icons slightly smaller
    const ImVec2 icon_size = ImVec2(icon_size_full.x * icon_scale, icon_size_full.y * icon_scale);

    // Calculate size with consistent padding
    ImVec2 pos = window->DC.CursorPos;
    ImVec2 size = ImGui::CalcItemSize(size_arg,
        icon_size.x + style.FramePadding.x * 2.0f + 8.0f, // Added extra padding
        icon_size.y + style.FramePadding.y * 2.0f + 4.0f  // Added extra padding
    );

    const ImRect rect(pos, ImVec2(pos.x + size.x, pos.y + size.y));
    ImGui::ItemSize(rect, style.FramePadding.y);
    if (!ImGui::ItemAdd(rect, id))
        return false;

    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(rect, id, &hovered, &held, NULL);

    if (hovered || selected) {
        ImGui::SetMouseCursor(ImGuiMouseCursor_Hand);
    }

    // Get or create animation state
    auto it_anim = button_anim_map.find(id);
    if (it_anim == button_anim_map.end()) {
        button_anim_map.insert({ id, ButtonAnimState() });
        it_anim = button_anim_map.find(id);
    }

    // Animation system matching tab design exactly
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity,
        (selected ? 0.04f : hovered ? 0.01f : 0.0f),
        0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Icon color animation matching tab design
    it_anim->second.icon_color = ImLerp(
        it_anim->second.icon_color,
        selected || hovered ? gui.main : ImColor(1.0f, 1.0f, 1.0f, 0.7f),
        GetAnimSpeed()
    );

    // Draw background rectangle exactly like tabs
    window->DrawList->AddRectFilled(rect.Min, rect.Max,
        ImColor(1.0f, 1.0f, 1.0f, it_anim->second.element_opacity), 3.0f);

    // Draw border exactly like tabs
    ImVec4 border_color = selected || hovered ?
        ImVec4(ImColor(255, 255, 255, 10)) :
        ImVec4(ImColor(255, 255, 255, 10));
    window->DrawList->AddRect(rect.Min, rect.Max,
        ImGui::ColorConvertFloat4ToU32(border_color), 3.0f, 0, 1.0f);

    // Draw icon shadow exactly like tabs
    ImVec2 icon_center = ImVec2(
        rect.GetCenter().x,
        rect.GetCenter().y
    );

    window->DrawList->AddShadowCircle(
        icon_center,
        9.f,
        ImGui::GetColorU32(it_anim->second.icon_color),
        40,
        ImVec2(0, 0),
        0,
        360
    );

    // Center icon perfectly
    ImVec2 icon_pos = ImVec2(
        rect.GetCenter().x - icon_size.x * 0.5f,
        rect.GetCenter().y - icon_size.y * 0.5f
    );

    // Draw icon with scaling for smaller size
    ImGui::PushFont(iconsBig);
    ImGui::SetWindowFontScale(icon_scale); // Apply scaling
    window->DrawList->AddText(
        icon_pos,
        ImGui::GetColorU32(it_anim->second.icon_color),
        icon
    );
    ImGui::SetWindowFontScale(1.0f); // Reset font scale
    ImGui::PopFont();

    return pressed;
}

void nav_elements::BeginGroup() {
    ImGuiContext& g = *GImGui;
    ImGuiWindow* window = g.CurrentWindow;

    g.GroupStack.resize(g.GroupStack.Size + 1);
    ImGuiGroupData& group_data = g.GroupStack.back();
    group_data.WindowID = window->ID;
    group_data.BackupCursorPos = window->DC.CursorPos;
    group_data.BackupCursorMaxPos = window->DC.CursorMaxPos;
    group_data.BackupIndent = window->DC.Indent;
    group_data.BackupGroupOffset = window->DC.GroupOffset;
    group_data.BackupCurrLineSize = window->DC.CurrLineSize;
    group_data.BackupCurrLineTextBaseOffset = window->DC.CurrLineTextBaseOffset;
    group_data.BackupActiveIdIsAlive = g.ActiveIdIsAlive;
    group_data.BackupHoveredIdIsAlive = g.HoveredId != 0;
    group_data.BackupActiveIdPreviousFrameIsAlive = g.ActiveIdPreviousFrameIsAlive;
    group_data.EmitItem = true;

    window->DC.GroupOffset.x = window->DC.CursorPos.x - window->Pos.x - window->DC.ColumnsOffset.x;
    window->DC.Indent = window->DC.GroupOffset;
    window->DC.CursorMaxPos = window->DC.CursorPos;
    window->DC.CurrLineSize = ImVec2(0.0f, 0.0f);
    if (g.LogEnabled) g.LogLinePosY = -FLT_MAX;
}

void nav_elements::EndGroup() {
    ImGuiContext& g = *GImGui;
    ImGuiWindow* window = g.CurrentWindow;
    IM_ASSERT(g.GroupStack.Size > 0);

    ImGuiGroupData& group_data = g.GroupStack.back();
    IM_ASSERT(group_data.WindowID == window->ID);

    if (window->DC.IsSetPos) ErrorCheckUsingSetCursorPosToExtendParentBoundaries();

    ImRect group_bb(group_data.BackupCursorPos, ImMax(window->DC.CursorMaxPos, group_data.BackupCursorPos));

    window->DC.CursorPos = group_data.BackupCursorPos;
    window->DC.CursorMaxPos = ImMax(group_data.BackupCursorMaxPos, window->DC.CursorMaxPos);
    window->DC.Indent = group_data.BackupIndent;
    window->DC.GroupOffset = group_data.BackupGroupOffset;
    window->DC.CurrLineSize = group_data.BackupCurrLineSize;
    window->DC.CurrLineTextBaseOffset = group_data.BackupCurrLineTextBaseOffset;
    if (g.LogEnabled) g.LogLinePosY = -FLT_MAX;

    if (!group_data.EmitItem) {
        g.GroupStack.pop_back();
        return;
    }

    window->DC.CurrLineTextBaseOffset = ImMax(window->DC.PrevLineTextBaseOffset,
        group_data.BackupCurrLineTextBaseOffset);
    ItemSize(group_bb.GetSize());
    ItemAdd(group_bb, 0, NULL, ImGuiItemFlags_NoTabStop);

    const bool group_contains_curr_active_id =
        (group_data.BackupActiveIdIsAlive != g.ActiveId) && (g.ActiveIdIsAlive == g.ActiveId) && g.ActiveId;
    const bool group_contains_prev_active_id =
        (group_data.BackupActiveIdPreviousFrameIsAlive == false) && (g.ActiveIdPreviousFrameIsAlive == true);
    if (group_contains_curr_active_id) g.LastItemData.ID = g.ActiveId;
    else if (group_contains_prev_active_id) g.LastItemData.ID = g.ActiveIdPreviousFrame;
    g.LastItemData.Rect = group_bb;

    const bool group_contains_curr_hovered_id = (group_data.BackupHoveredIdIsAlive == false) && g.HoveredId != 0;
    if (group_contains_curr_hovered_id) g.LastItemData.StatusFlags |= ImGuiItemStatusFlags_HoveredWindow;

    if (group_contains_curr_active_id && g.ActiveIdHasBeenEditedThisFrame)
        g.LastItemData.StatusFlags |= ImGuiItemStatusFlags_Edited;

    g.LastItemData.StatusFlags |= ImGuiItemStatusFlags_HasDeactivated;
    if (group_contains_prev_active_id && g.ActiveId != g.ActiveIdPreviousFrame)
        g.LastItemData.StatusFlags |= ImGuiItemStatusFlags_Deactivated;

    g.GroupStack.pop_back();
}

const ImWchar* nav_elements::GetGlyphRangesChinese()
{
    static const ImWchar ranges[] =
    {
        0x0020, 0x00FF, // Basic Latin + Latin Supplement
        0x3000, 0x30FF, // Punctuations, Hiragana, Katakana
        0x31F0, 0x31FF, // Katakana Phonetic Extensions
        0xFF00, 0xFFEF, // Half-width characters
        0x4e00, 0x9FAF, // CJK Ideograms
        0,
    };
    return &ranges[0];
}


void nav_elements::EndCombo() {
    gui.combo_window = ImGui::GetCurrentWindow();
    End();
}

void nav_elements::MultiCombo(const char* label, bool variable[], const char* labels[], int count, const char* icon) {
    ImGuiContext& g = *GImGui;

    std::string preview = "None";

    for (auto i = 0, j = 0; i < count; i++) {
        if (variable[i]) {
            if (j)
                preview += (", ") + (std::string)labels[i];
            else
                preview = labels[i];

            j++;
        }
    }

  /*  if (nav_elements::BeginCombo(label, preview.c_str(), icon,count)) {
        for (auto i = 0; i < count; i++) {
            PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(15, 15));
            PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
            nav_elements::Selectable(labels[i], &variable[i], ImGuiSelectableFlags_DontClosePopups);
            PopStyleVar(2);
        }
        End();
    }*/

    preview = ("None");
}

bool nav_elements::BeginComboPreview() {
    ImGuiContext& g = *GImGui;
    ImGuiWindow* window = g.CurrentWindow;
    ImGuiComboPreviewData* preview_data = &g.ComboPreviewData;

    if (window->SkipItems || !(g.LastItemData.StatusFlags & ImGuiItemStatusFlags_Visible)) return false;

    IM_ASSERT(g.LastItemData.Rect.Min.x == preview_data->PreviewRect.Min.x &&
        g.LastItemData.Rect.Min.y == preview_data->PreviewRect.Min.y);

    if (!window->ClipRect.Overlaps(preview_data->PreviewRect)) return false;

    preview_data->BackupCursorPos = window->DC.CursorPos;
    preview_data->BackupCursorMaxPos = window->DC.CursorMaxPos;
    preview_data->BackupCursorPosPrevLine = window->DC.CursorPosPrevLine;
    preview_data->BackupPrevLineTextBaseOffset = window->DC.PrevLineTextBaseOffset;
    preview_data->BackupLayout = window->DC.LayoutType;
    window->DC.CursorPos = preview_data->PreviewRect.Min + g.Style.FramePadding;
    window->DC.CursorMaxPos = window->DC.CursorPos;
    window->DC.LayoutType = ImGuiLayoutType_Horizontal;
    window->DC.IsSameLine = false;
    PushClipRect(preview_data->PreviewRect.Min, preview_data->PreviewRect.Max, true);

    return true;
}

void nav_elements::EndComboPreview() {
    ImGuiContext& g = *GImGui;
    ImGuiWindow* window = g.CurrentWindow;
    ImGuiComboPreviewData* preview_data = &g.ComboPreviewData;

    ImDrawList* draw_list = window->DrawList;
    if (window->DC.CursorMaxPos.x < preview_data->PreviewRect.Max.x &&
        window->DC.CursorMaxPos.y < preview_data->PreviewRect.Max.y)
        if (draw_list->CmdBuffer.Size > 1) {
            draw_list->_CmdHeader.ClipRect = draw_list->CmdBuffer[draw_list->CmdBuffer.Size -
                1].ClipRect = draw_list->CmdBuffer[
                    draw_list->CmdBuffer.Size - 2].ClipRect;
            draw_list->_TryMergeDrawCmds();
        }
    PopClipRect();
    window->DC.CursorPos = preview_data->BackupCursorPos;
    window->DC.CursorMaxPos = ImMax(window->DC.CursorMaxPos, preview_data->BackupCursorMaxPos);
    window->DC.CursorPosPrevLine = preview_data->BackupCursorPosPrevLine;
    window->DC.PrevLineTextBaseOffset = preview_data->BackupPrevLineTextBaseOffset;
    window->DC.LayoutType = preview_data->BackupLayout;
    window->DC.IsSameLine = false;
    preview_data->PreviewRect = ImRect();
}

static const char* Items_ArrayGetter(void* data, int idx) {
    const char* const* items = (const char* const*)data;
    return items[idx];
}

static const char* Items_SingleStringGetter(void* data, int idx) {
    const char* items_separated_by_zeros = (const char*)data;
    int items_count = 0;
    const char* p = items_separated_by_zeros;
    while (*p) {
        if (idx == items_count)
            break;
        p += strlen(p) + 1;
        items_count++;
    }
    return *p ? p : NULL;
}
static float CalcMaxPopupHeightFromItemCount(int items_count) {
    ImGuiContext& g = *GImGui;
    if (items_count <= 0)
        return FLT_MAX;
    return (g.FontSize + g.Style.ItemSpacing.y) * items_count - g.Style.ItemSpacing.y + (g.Style.WindowPadding.y * 2);
}
bool nav_elements::Combo(const char* label,
    int* current_item,
    const char* (*getter)(void* user_data, int idx),
    void* user_data,
    int items_count,
    const char* description, const char* icon,
    int popup_max_height_in_items) {


    ImGuiContext& g = *GImGui;

    const char* preview_value = NULL;
    if (*current_item >= 0 && *current_item < items_count) {
        preview_value = getter(user_data, *current_item);
    } else {
    }

    if (popup_max_height_in_items != -1 && !(g.NextWindowData.Flags & ImGuiNextWindowDataFlags_HasSizeConstraint)) {
        SetNextWindowSizeConstraints(ImVec2(0, 0),
            ImVec2(FLT_MAX, CalcMaxPopupHeightFromItemCount(popup_max_height_in_items)));
    }

    if (!nav_elements::BeginCombo(label, preview_value, items_count, description, icon, ImGuiComboFlags_None)) {
        return false;
    }

    bool value_changed = false;
    PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(15, 15));
    for (int i = 0; i < items_count; i++) {
        const char* item_text = getter(user_data, i);
        if (item_text == NULL)
            item_text = "*Unknown item*";

        PushID(i);
        const bool item_selected = (i == *current_item);
        if (nav_elements::SelectableEx(item_text, item_selected) && *current_item != i) {
            value_changed = true;
            *current_item = i;
        }
        if (item_selected)
            SetItemDefaultFocus();
        PopID();
    }
    PopStyleVar();

    EndCombo();

    if (value_changed) {
        MarkItemEdited(g.LastItemData.ID);
    }

    return value_changed;
}

bool nav_elements::Combo(const char* label,
    int* current_item,
    const char* const items[],
    int items_count,

    const char* description, const char* icon,
    int height_in_items) {
    const bool value_changed = Combo(label,
        current_item,
        Items_ArrayGetter,
        (void*)items,
        items_count,
        description,icon,
        height_in_items);
    return value_changed;
}

bool nav_elements::Combo(const char* label,
    int* current_item,
    const char* items_separated_by_zeros,
    const char* description, const char* icon,
    int height_in_items) {
    int items_count = 0;
    const char* p = items_separated_by_zeros;
    while (*p) {
        p += strlen(p) + 1;
        items_count++;
    }
    bool value_changed = Combo(label,
        current_item,
        Items_SingleStringGetter,
        (void*)items_separated_by_zeros,
        items_count,
        description,icon,
        height_in_items);
    return value_changed;
}

struct select_state {
    ImVec4 text, background, line;
    float circle_radius, text_offset;
};

// Add after the existing TabVS2 implementation but before Checkbox

// Map to store animation states for horizontal tabs
std::map<ImGuiID, tab_elements> h_anim;

bool nav_elements::HorizontalTab(const char* label, int* v, int number) {
    // Check if the current tab is active
    bool is_active = (*v == number);

    // Call the horizontal tab function with the icon and label
    if (HorizontalTabV2(label, is_active)) {
        // If the tab is pressed, update the selected tab
        *v = number;
        return true;
    }

    return false;
}


bool nav_elements::HorizontalTabV2(const char* name, bool boolean)
{
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(name);

    const ImVec2 label_size = ImGui::CalcTextSize(name);
    ImVec2 pos = window->DC.CursorPos;

    const ImRect rect(pos, ImVec2(pos.x + 150, pos.y + 42));
    ImGui::ItemSize(ImVec4(rect.Min.x, rect.Min.y, rect.Max.x, rect.Max.y + 5), style.FramePadding.y);
    if (!ImGui::ItemAdd(rect, id))
        return false;

    bool hovered, held;
    bool pressed = ImGui::ButtonBehavior(rect, id, &hovered, &held, NULL);

    if (hovered || boolean) {
        ImGui::SetMouseCursor(ImGuiMouseCursor_Hand);
    }

    auto it_anim = anim_2.find(id);
    if (it_anim == anim_2.end()) {
        anim_2.insert({ id, { 0.0f, 0.0f, 0.0f, ImVec4(1.0f, 1.0f, 1.0f, 0.7f), 0.0f } });
        it_anim = anim_2.find(id);
    }

    ImVec2 size({ window->Size.x, 24 });
    if (pressed)
        content_anim = 0.f;

    // Animation logic
    it_anim->second.line_opacity = ImLerp(it_anim->second.line_opacity, (boolean || hovered) ? 1.0f : 0.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity, (boolean ? 0.04f : hovered ? 0.01f : 0.0f), 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.rect_opacity = ImLerp(it_anim->second.rect_opacity, (boolean ? 1.0f : 0.0f), 0.15f * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.text_opacity = ImLerp(it_anim->second.text_opacity, (boolean ? 1.0f : hovered ? 0.5f : 0.3f), 0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Icon color animation
    it_anim->second.icon_color = ImLerp(
        it_anim->second.icon_color,
        boolean || hovered ? gui.main : ImColor(1.0f, 1.0f, 1.0f, 0.7f),
        GetAnimSpeed()
    );

    // Background rectangle
    window->DrawList->AddRectFilled(rect.Min, rect.Max, ImColor(1.0f, 1.0f, 1.0f, it_anim->second.element_opacity), 3.0f);

    // Border
    ImVec4 border_color = boolean || hovered ? ImVec4(ImColor(255, 255, 255, 10)) : ImVec4(ImColor(255, 255, 255, 10));
    window->DrawList->AddRect(rect.Min, rect.Max, ImGui::ColorConvertFloat4ToU32(border_color), 3.0f, 0, 1.0f);

    // Text and icon positioning
    const ImVec2 label_pos = ImVec2(rect.GetCenter().x - label_size.x / 2, rect.GetCenter().y - label_size.y / 2 - 2);
    // Label text
    window->DrawList->AddText(label_pos, ImColor(1.0f, 1.0f, 1.0f, it_anim->second.text_opacity), name);

    // Horizontal bottom line parameters
    const float line_height = 1.0f;
    const float line_padding = 10.0f;
    ImVec2 line_start = { rect.Min.x + line_padding, rect.Max.y - 2 };
    ImVec2 line_end = { rect.Max.x - line_padding, rect.Max.y - 2 + line_height };

    // Shadow for horizontal line
    if (boolean || hovered) {
        window->DrawList->AddShadowRect(
            line_start,
            line_end,
            gui.main,
            25.f,
            ImVec2(0, 2),
            ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight,
            60.f
        );
    }

    // Main glow line
    ImColor glow_color = ImColor(accent_color[2], accent_color[1], accent_color[0], it_anim->second.line_opacity);
    window->DrawList->AddRectFilled(
        line_start,
        line_end,
        glow_color,
        360.f,
        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
    );

    // Subtle background line
    window->DrawList->AddRectFilled(
        line_start,
        line_end,
        ImColor(1.0f, 1.0f, 1.0f, it_anim->second.line_opacity * 0.2f),
        360.f,
        ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight
    );

    return pressed;
}


void nav_elements::RenderExpandedContentAnimated(const std::string& featureName, std::function<void()> contentRenderer) {
    // Renders animated expanded content with proper layout alignment and spacing
    // Fixed spacing issues:
    // - Added 12px padding inside expanded container
    // - Added 8px spacing between nested elements
    // - Each nested element has its own background/border
    // - Proper margins from container edges
    // - Uniform spacing system for all nested components

    // Get expansion state for this feature
    float expansionAnimState = GetFeatureExpansionState(featureName);

    // Only render if there's meaningful animation state
    if (expansionAnimState < 0.02f) {
        return;
    }

    // Cache content height to prevent layout jumps and double rendering
    static std::map<std::string, float> cachedContentHeights;

    float contentHeight = 0.0f;

    // Standardized spacing constants for consistent layout
    const float kContainerPadding = 12.0f;     // Padding inside expanded container
    const float kElementSpacing = 8.0f;        // Spacing between nested elements
    const float kElementPadding = 16.0f;       // Padding inside each nested element
    const float kContainerMargin = 8.0f;       // Margin from main container edges

    // Only measure height if we don't have it cached or if fully expanded (for dynamic content)
    if (cachedContentHeights.find(featureName) == cachedContentHeights.end() || expansionAnimState > 0.99f) {
        ImVec2 contentStart = ImGui::GetCursorPos();

        // Measure content height (invisible rendering with clip to prevent artifacts)
        ImGui::PushStyleVar(ImGuiStyleVar_Alpha, 0.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(kContainerPadding, kContainerPadding));
        ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(kElementSpacing, kElementSpacing));
        ImGui::PushStyleVar(ImGuiStyleVar_ItemInnerSpacing, ImVec2(8, 4));
        ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(8, 4));
        ImGui::PushClipRect(ImVec2(-10000, -10000), ImVec2(-9000, -9000), true);

        // Add container padding before content
        ImGui::Dummy(ImVec2(0, 7)); // Container padding after content
        contentRenderer();
        ImGui::Dummy(ImVec2(0, 7)); // Container padding after content

        ImGui::PopClipRect();
        contentHeight = ImGui::GetCursorPos().y - contentStart.y;
        ImGui::PopStyleVar(5);

        // Cache the height (already includes all spacing)
        cachedContentHeights[featureName] = contentHeight;

        // Reset cursor to start position
        ImGui::SetCursorPos(contentStart);
    }
    else {
        // Use cached height
        contentHeight = cachedContentHeights[featureName];
    }

    // Apply smooth easing animations
    float heightEasing = expansionAnimState * expansionAnimState * (3.0f - 2.0f * expansionAnimState); // Smoothstep
    float opacityEasing = expansionAnimState * expansionAnimState; // Quadratic easing for opacity
    float currentHeight = contentHeight * heightEasing;

    // Only render visible content if height is meaningful
    if (currentHeight > 1.0f && opacityEasing > 0.05f) {
        // Use EXACT same width calculation as CheckboxComponent to maintain consistency
        const ImGuiStyle& style = ImGui::GetStyle();
        ImGuiContext& g = *GImGui;
        const float content_width = GetContentRegionMax().x - style.GrabMinSize - g.Style.WindowPadding.x;
        const float component_height = 60.f;
        // Vertical spacing before expanded area (match CheckboxComponent row spacing)
        //ImGui::SetCursorPosY(ImGui::GetCursorPosY() + style.ItemSpacing.y);
        // Apply opacity to the entire expanded area
        ImGui::PushStyleVar(ImGuiStyleVar_Alpha, opacityEasing);
        // Apply container margin from edges (left/right)
        ImGui::SetCursorPosX(ImGui::GetCursorPosX() + kContainerMargin);
        // Begin the expanded container group
        ImGui::BeginGroup();
        // Get container dimensions for background
        ImVec2 containerStart = ImGui::GetCursorScreenPos();
        // Apply styles for consistent nested element layout
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(kContainerPadding, kContainerPadding));
        ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(kElementSpacing, kElementSpacing));
        ImGui::PushStyleVar(ImGuiStyleVar_ItemInnerSpacing, ImVec2(8, 4));
        ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(8, 4));
        // Create a child window for proper nested element rendering
        const float childWidth = content_width - (kContainerMargin * 2.0f);
        const float childHeight = currentHeight - (kElementSpacing * 2.0f);
        ImGui::BeginChild(("expanded_" + featureName).c_str(),
            ImVec2(childWidth, childHeight),
            true, // Show border
            ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollWithMouse);
        // Only render content if opacity is sufficient
        if (opacityEasing > 0.1f) {
            ImGui::PushStyleColor(ImGuiCol_ChildBg, ImGui::GetStyleColorVec4(ImGuiCol_FrameBg));
            ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, style.FrameRounding);
            contentRenderer();
            ImGui::PopStyleColor(); // Pop child bg color
            ImGui::PopStyleVar(); // Pop child rounding
        }
        ImGui::EndChild();
        ImGui::PopStyleVar(4); // Pop the layout style vars
        ImGui::EndGroup();
        // Vertical spacing after expanded area (match CheckboxComponent row spacing)
        //ImGui::SetCursorPosY(ImGui::GetCursorPosY() + style.ItemSpacing.y);
        ImGui::PopStyleVar(); // Pop the alpha style var
        // Handle click-outside-to-close behavior
        ImVec2 expandedMin = containerStart;
        ImVec2 expandedMax = ImVec2(containerStart.x + childWidth, containerStart.y + childHeight);
        bool mouseInExpandedArea = ImGui::IsMouseHoveringRect(expandedMin, expandedMax);
        
        // Check if any popup is open to prevent closing expanded area during popup interactions
        bool anyPopupOpen = gui.darkoverlay;
        
        if (ImGui::IsMouseClicked(ImGuiMouseButton_Left) && !mouseInExpandedArea && !anyPopupOpen) {
            g_expandedFeaturesMap[featureName] = false;
        }
    }

    // Clear cached height when animation is complete and closing
    bool isExpanded = g_expandedFeaturesMap[featureName];
    if (expansionAnimState < 0.02f && !isExpanded) {
        cachedContentHeights.erase(featureName);
    }
}

// Updated begin_state struct with auto-close support
struct begin_state {
    ImVec4 background_color, text, outline, background_rect_color, combo_col, preview_col, icon_color, rect_shadow_color, rect_color, combo_rect_color;
    float alpha = 0.f, shadow_opticaly;
    bool opened_combo = false, hovered = false, want_close = false, window_hovered = false;
    float arrow_roll, preview_value_size;
    ImVec2 combo_size;
    ImGuiWindow* window;
    ImVec2 rotation_window;
    char search[64];

    // New variables for smooth animation and auto-close
    float dropdown_alpha = 0.0f;
    bool was_clicked_this_frame = false;
    bool selection_made = false;
    int pending_selection = -1; // Store which item was selected
};

// Complete Combo function that handles everything internally with toggle and auto-close
bool nav_elements::ComboEx(const char* label, int* current_item, const char* const items[], int items_count, const char* description,const char* icon ) {
    // Input validation
    if (!label || !current_item || !items || items_count <= 0 || !description) {
        return false;
    }

    // Get preview value safely
    const char* preview_value = (*current_item >= 0 && *current_item < items_count) ? items[*current_item] : "None";

    bool value_changed = false;

    // Start the combo box
    if (nav_elements::BeginCombo(label, preview_value, *current_item, description, icon )) {

        // Draw all selectable items
        for (int i = 0; i < items_count; i++) {
            const bool is_selected = (i == *current_item);

            // Use PushID to ensure unique IDs for each selectable item
            ImGui::PushID(i);

            if (nav_elements::SelectableEx(items[i], is_selected, 0, ImVec2(0, 0))) {
                // Item was clicked - immediately update the current item
                *current_item = i;
                value_changed = true;

                // Force immediate closure of the combo dropdown
                ImGuiContext& g = *GImGui;
                ImGuiWindow* window = GetCurrentWindow();
                const ImGuiID combo_id = window->GetID(label);
                
                // Access the animation state and force closure
                static std::map<ImGuiID, begin_state> anim;
                auto it_anim = anim.find(combo_id);
                if (it_anim != anim.end()) {
                    // Immediately close the combo
                    it_anim->second.want_close = true;
                    it_anim->second.opened_combo = false;
                    it_anim->second.dropdown_alpha = 0.0f;
                    it_anim->second.combo_size = ImVec2(0.f, 0.f);
                }
                
                // Force close the current popup window
                if (ImGui::IsPopupOpen("", ImGuiPopupFlags_AnyPopupId)) {
                    ImGui::CloseCurrentPopup();
                }
                
                // Break out of the loop since we've made a selection
                break;
            }

            ImGui::PopID();
        }

        ImGui::End();
    }

    return value_changed;
}

// Updated BeginCombo with proper auto-close support
bool nav_elements::BeginCombo(const char* label, const char* preview_value, int val, const char* description, const char* icon, ImGuiComboFlags flags) {
    // --- Input validation to prevent crashes ---
    if (!label || !preview_value || !description) {
        return false;
    }

    // --- Early return check ---
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems)
        return false;

    // --- Setup and ID generation ---
    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(label);
    std::string label_str = label;

    g.NextWindowData.ClearFlags();

    // --- Layout calculations ---
    const ImVec2 pos = window->DC.CursorPos;
    const float content_width = GetContentRegionMax().x - style.GrabMinSize - g.Style.WindowPadding.x;
    const float component_height = 60.f;
    const float comboSelectorSize = 50.f;
    const float comboSelectorHeight = 26.f;

    const ImVec2 total_size(content_width, component_height);
    const ImRect total_bb(pos, ImVec2(pos.x + total_size.x, pos.y + total_size.y));
    ItemSize(total_bb, 0.f);

    if (!ItemAdd(total_bb, id, &total_bb))
        return false;

    // --- Animation state management ---
    static std::map<ImGuiID, begin_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, begin_state() });
        it_anim = anim.find(id);
    }

    // --- Combo selector dimensions and positioning ---
    const float check_spacing = (total_bb.GetHeight() - comboSelectorHeight) / 2;
    const ImRect combo_bb(
        ImVec2(total_bb.Max.x - check_spacing - comboSelectorSize - it_anim->second.preview_value_size,
            total_bb.Max.y - check_spacing - comboSelectorHeight),
        ImVec2(total_bb.Max.x - check_spacing, total_bb.Max.y - check_spacing)
    );

    // --- Input handling ---
    bool hovered, held;
    bool pressed = ButtonBehavior(total_bb, id, &hovered, &held);

    // Update combo hover state
    it_anim->second.hovered = ImGui::IsMouseHoveringRect(combo_bb.Min, combo_bb.Max, true);

    // Reset click flag each frame
    it_anim->second.was_clicked_this_frame = false;

    // Handle combo opening/closing with proper toggling
    if (hovered && g.IO.MouseClicked[0]) {
        it_anim->second.was_clicked_this_frame = true;
        it_anim->second.opened_combo = !it_anim->second.opened_combo; // Toggle state

        // Reset flags when opening
        if (it_anim->second.opened_combo) {
            it_anim->second.want_close = false;
            it_anim->second.selection_made = false;
            // Combo has its own overlay system - don't use global dark overlay
        }
    }
    
    // Enhanced opening animation with improved timing for polished experience
    static std::map<ImGuiID, float> open_delay;
    auto delay_it = open_delay.find(id);
    if (delay_it == open_delay.end()) {
        open_delay.insert({ id, 0.0f });
        delay_it = open_delay.find(id);
    }
    
    // Reset delay when closing
    if (!it_anim->second.opened_combo) {
        delay_it->second = 0.0f;
    }
    
    // Apply refined delay timing for more polished feel
    if (it_anim->second.opened_combo && delay_it->second < 0.12f) { // Slightly longer 0.12 second delay
        delay_it->second += g.IO.DeltaTime;
        // Smooth fade-in during delay period
        float delay_progress = delay_it->second / 0.12f;
        if (delay_it->second < 0.12f) {
            it_anim->second.dropdown_alpha = delay_progress * 0.3f; // Gentle fade-in start
            it_anim->second.combo_size = ImVec2(0.f, delay_progress * 5.f); // Subtle size hint
        }
    }

    // Close combo when clicking outside (but not when just toggling)
    if (it_anim->second.opened_combo && g.IO.MouseClicked[0] && !it_anim->second.hovered &&
        !it_anim->second.was_clicked_this_frame && !it_anim->second.window_hovered) {
        it_anim->second.want_close = true;
    }

    // Handle close logic - close when animation is nearly complete
    if (it_anim->second.want_close && it_anim->second.combo_size.y < 5.f && it_anim->second.opened_combo) {
        it_anim->second.opened_combo = false;
        it_anim->second.want_close = false;
        it_anim->second.selection_made = false;
        // Combo has its own overlay system - don't use global dark overlay
    }

    // --- Text positioning ---
    const ImVec2 description_size = CalcTextSize(description, NULL, true);
    const ImVec2 label_size = CalcTextSize(label, NULL, true);
    const ImVec2 label_pos = ImVec2(
        total_bb.Min.x + 60.f + (total_bb.GetSize().y - (label_size.y + description_size.y)) / 2,
        total_bb.GetCenter().y - (label_size.y + description_size.y) / 2
    );

    // --- Animation updates ---
    const float anim_speed = GetAnimSpeed();

    // Preview value size animation
    it_anim->second.preview_value_size = ImLerp(it_anim->second.preview_value_size,
        CalcTextSize(preview_value).x, anim_speed);

    // Arrow rotation animation
    it_anim->second.arrow_roll = ImLerp(it_anim->second.arrow_roll,
        it_anim->second.opened_combo ? -1.f : 1.f, g.IO.DeltaTime * 6.f);

    // Enhanced dropdown alpha animation with improved easing curves
    float target_alpha = (it_anim->second.opened_combo && !it_anim->second.want_close) ? 1.0f : 0.0f;
    float alpha_speed = it_anim->second.opened_combo ? g.IO.DeltaTime * 12.f : g.IO.DeltaTime * 15.f; // Faster closing
    it_anim->second.dropdown_alpha = ImLerp(it_anim->second.dropdown_alpha, target_alpha, alpha_speed);

    // Selector color animations
    it_anim->second.preview_col = ImLerp(it_anim->second.preview_col,
        it_anim->second.opened_combo ? gui.main : gui.text[1], g.IO.DeltaTime * 6.f);

    // Fixed color logic - ensure consistent colors and prevent red background
    it_anim->second.combo_col = ImLerp(it_anim->second.combo_col,
        it_anim->second.opened_combo ? func.GetColorWithAlpha(gui.main, 0.3f) : gui.second,
        g.IO.DeltaTime * 6.f);

    it_anim->second.combo_rect_color = ImLerp(it_anim->second.combo_rect_color, gui.checkboxstrokeactive, anim_speed / 3);

    // Background and border animations
    it_anim->second.background_color = ImLerp(it_anim->second.background_color,
        hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(25, 19, 15, 155), 0.3f), anim_speed);

    it_anim->second.background_rect_color = ImLerp(it_anim->second.background_rect_color,
        hovered ? gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f), anim_speed);

    // Icon color animation
    it_anim->second.icon_color = ImLerp(it_anim->second.icon_color,
        hovered ? func.GetDarkColor(gui.main) : gui.main, anim_speed);

    // Left side rectangle color
    it_anim->second.rect_color = ImLerp(it_anim->second.rect_color,
        hovered ? gui.main : func.GetColorWithAlpha(gui.main, 0.3f), anim_speed);

    it_anim->second.rect_shadow_color = ImLerp(it_anim->second.rect_shadow_color,
        hovered ? gui.main : func.GetColorWithAlpha(ImColor(0, 0, 0, 255), 0.3f), anim_speed);

    // Enhanced combo dropdown size animation with polished easing curves
    ImVec2 size_expected = { 0.f, 35.f };
    float size_lerp_speed = it_anim->second.opened_combo ? 15.f : 18.f; // Faster and more responsive
    
    // Advanced easing with smooth acceleration and deceleration
    ImVec2 target_size = it_anim->second.opened_combo && !it_anim->second.want_close ? size_expected : ImVec2(0.f, 0.f);
    ImVec2 size_diff = ImVec2(target_size.x - it_anim->second.combo_size.x, target_size.y - it_anim->second.combo_size.y);
    
    // Apply smoothstep easing for more polished animation
    float easing_factor = g.IO.DeltaTime * size_lerp_speed;
    if (it_anim->second.opened_combo) {
        // Opening: smooth acceleration
        float progress = 1.0f - (size_diff.y / size_expected.y);
        easing_factor *= (1.0f + progress * 0.5f); // Faster as it approaches target
    } else {
        // Closing: immediate response
        easing_factor *= 1.2f;
    }
    
    it_anim->second.combo_size = ImLerp(it_anim->second.combo_size, target_size, easing_factor);

    // Window rotation animation
    if (it_anim->second.opened_combo) {
        EasingAnimationV2("combo_rotation" + label_str,
            &it_anim->second.rotation_window,
            ImVec2(IM_PI / 2, 0.f),
            0.008f,
            imanim::EasingCurve::Type::InOutBack,
            -1);
    }
    else {
        EasingAnimationV2("combo_rotation" + label_str,
            &it_anim->second.rotation_window,
            ImVec2(IM_PI / 2 + 0.1f, 0.f),
            0.5f,
            imanim::EasingCurve::Type::InOutBack,
            -1);
    }

    // --- Rendering ---
    // Main background
    window->DrawList->AddRectFilled(total_bb.Min, total_bb.Max,
        GetColorU32(it_anim->second.background_color), 3.0f);

    // Main border
    window->DrawList->AddRect(total_bb.Min, total_bb.Max,
        GetColorU32(it_anim->second.background_rect_color), 3.0f);

    // Left background rectangle with shadow
    GetWindowDrawList()->AddRectFilled(total_bb.Min,
        ImVec2(total_bb.Min.x + total_bb.GetSize().y, total_bb.Min.y + total_bb.GetSize().y),
        GetColorU32(it_anim->second.rect_color), style.FrameRounding, ImDrawFlags_RoundCornersLeft);

    window->DrawList->AddShadowRect(total_bb.Min,
        ImVec2(total_bb.Min.x + total_bb.GetSize().y - 5, total_bb.Max.y),
        GetColorU32(it_anim->second.rect_shadow_color), 20.f, ImVec2(0, 0), 0, ImDrawFlags_RoundCornersLeft);

    // Icon background shadow
    window->DrawList->AddShadowCircle(
        ImVec2(total_bb.Min.x + total_bb.GetSize().y / 2, total_bb.Min.y + total_bb.GetSize().y / 2),
        9.f, GetColorU32(it_anim->second.icon_color), 40, ImVec2(0, 0), 0, 360);

    // Combo selector background
    GetWindowDrawList()->AddRectFilled(combo_bb.Min, combo_bb.Max,
        GetColorU32(it_anim->second.combo_col), style.FrameRounding);
    GetWindowDrawList()->AddRect(combo_bb.Min, combo_bb.Max,
        GetColorU32(it_anim->second.combo_rect_color), style.FrameRounding);

    // Combo selector text
    GetWindowDrawList()->AddText(
        ImVec2(combo_bb.Min.x + 10,
            func.CalcTextPos(combo_bb.Min, combo_bb.Max, preview_value).y),
        GetColorU32(it_anim->second.preview_col), preview_value);

    // Dropdown arrow (animated)
    PushFont(iconsBig);
    const char* arrow_icon = it_anim->second.opened_combo ? ICON_UP_LINE : ICON_DOWN_LINE;
    ImVec2 arrow_pos = ImVec2(combo_bb.Max.x - CalcTextSize(arrow_icon).x - 4.f,
        combo_bb.GetCenter().y - CalcTextSize(arrow_icon).y / 2);
    GetWindowDrawList()->AddText(arrow_pos, GetColorU32(gui.main.Value), arrow_icon);
    PopFont();

    // Text labels
    GetWindowDrawList()->AddText(label_pos, gui.text[0], label);
    GetWindowDrawList()->AddText(label_pos + ImVec2(0.f, label_size.y), gui.text[1],
        getStringBeforeCaret(description) != nullptr ? getStringBeforeCaret(description) : description);

    // Custom icon rendering for description
    if (icon != nullptr) {
        PushFont(iconsBig);
        GetWindowDrawList()->AddText(
            func.CalcTextPos(total_bb.Min,
                ImVec2(total_bb.Min.x + total_bb.GetSize().y, total_bb.Min.y + total_bb.GetSize().y),
                icon),
            GetColorU32(it_anim->second.icon_color),
            icon);
        PopFont();
    }

    // --- Dropdown window handling ---
    if (!IsRectVisible(total_bb.Min, total_bb.Max + ImVec2(0, 2))) {
        it_anim->second.want_close = true;
        it_anim->second.combo_size.y = 0.f;
    }

    // Only show dropdown if alpha is significant and combo should be open
    if (it_anim->second.dropdown_alpha < 0.01f || (!it_anim->second.opened_combo && it_anim->second.combo_size.y < 5.f))
        return false;

    // Setup dropdown window
    ImGui::SetNextWindowPos(ImVec2(combo_bb.Min.x, combo_bb.Max.y + 5));

    ImGuiWindowFlags window_flags =
        ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize |
        ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoMove;

    // Dropdown window styling with alpha animation
    ImVec4 window_bg = func.ImColorToImVec4(func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.9f * it_anim->second.dropdown_alpha));
    PushStyleColor(ImGuiCol_WindowBg, window_bg);
    PushStyleVar(ImGuiStyleVar_WindowRounding, c::elements::rounding);
    PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
    PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(2, 2));
    PushStyleVar(ImGuiStyleVar_ScrollbarSize, 3.f);
    PushStyleVar(ImGuiStyleVar_Alpha, it_anim->second.dropdown_alpha);

    ImGui::SetNextWindowFocus();

    bool ret = Begin(label, NULL, window_flags);

    it_anim->second.window = ImGui::GetCurrentWindow();
    it_anim->second.window_hovered = ImGui::IsWindowHovered();

    PopStyleVar(5);
    PopStyleColor(1);

    return true;
}

// Updated SelectableEx that works with the auto-close system
bool nav_elements::SelectableEx(const char* label, bool selected, ImGuiSelectableFlags flags, const ImVec2& size_arg) {
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;

    ImGuiID id = window->GetID(label);
    ImVec2 label_size = CalcTextSize(label, NULL, true);
    ImVec2 size(size_arg.x != 0.0f ? size_arg.x : label_size.x, size_arg.y != 0.0f ? size_arg.y : label_size.y);
    ImVec2 pos = window->DC.CursorPos;
    pos.y += window->DC.CurrLineTextBaseOffset;
    ItemSize(size, 0.0f);

    const bool span_all_columns = (flags & ImGuiSelectableFlags_SpanAllColumns) != 0;
    const float min_x = span_all_columns ? window->ParentWorkRect.Min.x : pos.x;
    const float max_x = span_all_columns ? window->ParentWorkRect.Max.x : window->WorkRect.Max.x;
    if (size_arg.x == 0.0f || (flags & ImGuiSelectableFlags_SpanAvailWidth))
        size.x = ImMax(label_size.x, max_x - min_x);

    const ImVec2 text_min = pos;
    const ImVec2 text_max(min_x + size.x, pos.y + size.y);

    ImRect bb(min_x, pos.y, text_max.x, text_max.y);
    if ((flags & ImGuiSelectableFlags_NoPadWithHalfSpacing) == 0) {
        const float spacing_x = span_all_columns ? 0.0f : style.ItemSpacing.x;
        const float spacing_y = style.ItemSpacing.y;
        const float spacing_L = IM_TRUNC(spacing_x * 0.50f);
        const float spacing_U = IM_TRUNC(spacing_y * 0.50f);
        bb.Min.x -= spacing_L;
        bb.Min.y -= spacing_U;
        bb.Max.x += (spacing_x - spacing_L);
        bb.Max.y += (spacing_y - spacing_U);
    }

    const float backup_clip_rect_min_x = window->ClipRect.Min.x;
    const float backup_clip_rect_max_x = window->ClipRect.Max.x;
    if (span_all_columns) {
        window->ClipRect.Min.x = window->ParentWorkRect.Min.x;
        window->ClipRect.Max.x = window->ParentWorkRect.Max.x;
    }

    const bool disabled_item = (flags & ImGuiSelectableFlags_Disabled) != 0;
    const bool item_add = ItemAdd(bb, id, NULL, disabled_item ? ImGuiItemFlags_Disabled : ImGuiItemFlags_None);
    if (span_all_columns) {
        window->ClipRect.Min.x = backup_clip_rect_min_x;
        window->ClipRect.Max.x = backup_clip_rect_max_x;
    }

    if (!item_add) return false;

    const bool disabled_global = (g.CurrentItemFlags & ImGuiItemFlags_Disabled) != 0;
    if (disabled_item && !disabled_global) BeginDisabled();

    if (span_all_columns && window->DC.CurrentColumns) PushColumnsBackground();
    else if (span_all_columns && g.CurrentTable) TablePushBackgroundChannel();

    ImGuiButtonFlags button_flags = 0;
    if (flags & ImGuiSelectableFlags_NoHoldingActiveID) { button_flags |= ImGuiButtonFlags_NoHoldingActiveId; }
    if (flags & ImGuiSelectableFlags_NoSetKeyOwner) { button_flags |= ImGuiButtonFlags_NoSetKeyOwner; }
    if (flags & ImGuiSelectableFlags_SelectOnClick) { button_flags |= ImGuiButtonFlags_PressedOnClick; }
    if (flags & ImGuiSelectableFlags_SelectOnRelease) { button_flags |= ImGuiButtonFlags_PressedOnRelease; }
    if (flags & ImGuiSelectableFlags_AllowDoubleClick) {
        button_flags |= ImGuiButtonFlags_PressedOnClickRelease | ImGuiButtonFlags_PressedOnDoubleClick;
    }
    if ((flags & ImGuiSelectableFlags_AllowOverlap) ||
        (g.LastItemData.InFlags & ImGuiItemFlags_AllowOverlap)) {
        button_flags |= ImGuiButtonFlags_AllowOverlap;
    }

    const bool was_selected = selected;
    bool hovered, held, pressed = ButtonBehavior(bb, id, &hovered, &held, button_flags);

    if ((flags & ImGuiSelectableFlags_SelectOnNav) && g.NavJustMovedToId != 0 &&
        g.NavJustMovedToFocusScopeId == g.CurrentFocusScopeId)
        if (g.NavJustMovedToId == id) selected = pressed = true;

    if (pressed || (hovered && (flags & ImGuiSelectableFlags_SetNavIdOnHover))) {
        if (!g.NavDisableMouseHover && g.NavWindow == window && g.NavLayer == window->DC.NavLayerCurrent) {
            SetNavID(id,
                window->DC.NavLayerCurrent,
                g.CurrentFocusScopeId,
                WindowRectAbsToRel(window, bb));
            g.NavDisableHighlight = true;
        }
    }
    if (pressed) MarkItemEdited(id);

    if (selected != was_selected) g.LastItemData.StatusFlags |= ImGuiItemStatusFlags_ToggledSelection;

    if (g.NavId == id) RenderNavHighlight(bb, id, ImGuiNavHighlightFlags_NoRounding);

    if (span_all_columns && window->DC.CurrentColumns) PopColumnsBackground();
    else if (span_all_columns && g.CurrentTable) TablePopBackgroundChannel();

    static std::map<ImGuiID, select_state> anim;
    auto it_anim = anim.find(id);

    if (it_anim == anim.end()) {
        anim.insert({ id, select_state() });
        it_anim = anim.find(id);
        // Initialize text color properly to prevent rendering issues
        it_anim->second.text = gui.text[1]; // Default unselected text color
    }

    it_anim->second.text = ImLerp(it_anim->second.text, selected ? gui.text[0] : gui.text[1], GetAnimSpeed());
    it_anim->second.text_offset = ImLerp(it_anim->second.text_offset, selected ? 20.f : 4.5f, GetAnimSpeed());
    it_anim->second.background = ImLerp(it_anim->second.background,
        selected ? gui.third : func.GetColorWithAlpha(gui.third, 0.f),
        GetAnimSpeed());
    it_anim->second.line = ImLerp(it_anim->second.line,
        selected ? gui.main : func.GetColorWithAlpha(gui.main, 0.f),
        GetAnimSpeed());

    window->DrawList->AddRectFilled(bb.Min, bb.Max, GetColorU32(it_anim->second.background), c::elements::rounding);

    // Draw the vertical line with animation exactly matching TabVS2 style
    const float line_width = 1.0f;
    ImVec2 line_start = { bb.Max.x - 2.f, bb.Min.y + 10.f };
    ImVec2 line_end = { bb.Max.x - 2.f + line_width, bb.Max.y - 10.f };
    
    // Add shadow to the line (only when selected or hovered) - matching TabVS2
    if (selected || hovered) {
        window->DrawList->AddShadowRect(
            line_start,
            line_end,
            gui.main, // Use gui.main color like TabVS2
            25.f,
            ImVec2(0, 0),
            0,
            60.f
        );
    }

    // Add the glowing line with animated opacity - matching TabVS2 style
    ImColor glow_color = ImColor(accent_color[2], accent_color[1], accent_color[0],
                                selected ? 1.0f : hovered ? 0.8f : 0.0f);
    window->DrawList->AddRectFilled(
        line_start,
        line_end,
        glow_color,
        360.f,
        ImDrawFlags_RoundCornersTop | ImDrawFlags_RoundCornersBottom
    );

    // Always draw the text
    window->DrawList->AddText(ImVec2(bb.Min.x + 6.f, func.CalcTextPos(bb.Min, bb.Max, label).y),
        GetColorU32(it_anim->second.text),
        label);

    if (disabled_item && !disabled_global) EndDisabled();

    return pressed;
}

bool nav_elements::Selectable(const char* label, bool* p_selected, ImGuiSelectableFlags flags, const ImVec2& size_arg) {
    if (nav_elements::SelectableEx(label, *p_selected, flags, size_arg)) {
        *p_selected = !*p_selected;
        return true;
    }
    return false;
}

bool nav_elements::ColorPickerComponent(const char* name, std::vector<ColorState>& colorStates, const char* description,const char* icon ) {
    // --- Input validation to prevent crashes ---
    if (!name || colorStates.empty() || !description) {
        return false;
    }

    // --- Early return check ---
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems)
        return false;

    // --- Setup and ID generation ---
    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(name);
    const std::string featureName = std::string(name);

    // --- Layout calculations ---
    const ImVec2 pos = window->DC.CursorPos;
    const float content_width = GetContentRegionMax().x - style.GrabMinSize - g.Style.WindowPadding.x;
    const float component_height = 60.f;
    const float colorButtonSize = 26.f; // Match checkbox height
    const float colorButtonWidth = 50.f; // Match checkbox width
    const float elementPadding = 15.0f;

    // Calculate total width needed for color buttons
    float totalColorWidth = colorStates.size() * colorButtonWidth + (colorStates.size() - 1) * elementPadding;

    const ImVec2 total_size(content_width, component_height);
    const ImRect total_bb(pos, ImVec2(pos.x + total_size.x, pos.y + total_size.y));
    ItemSize(total_bb, 0.f);

    if (!ItemAdd(total_bb, id))
        return false;

    // --- Input handling ---
    bool hovered, held;
    bool pressed = ButtonBehavior(total_bb, id, &hovered, &held);

    // --- Text positioning ---
    const ImVec2 description_size = CalcTextSize(description, NULL, true);
    const ImVec2 label_size = CalcTextSize(name, NULL, true);
    const ImVec2 label_pos = ImVec2(
        total_bb.Min.x + 60.f + (total_bb.GetSize().y - (label_size.y + description_size.y)) / 2,
        total_bb.GetCenter().y - (label_size.y + description_size.y) / 2
    );

    // --- Animation state management ---
    static std::map<ImGuiID, check_state> anim;
    auto it_anim = anim.emplace(id, check_state()).first;

    static std::map<ImGuiID, edit_state> animation;
    edit_state& state = animation[id];

    // Update animation colors
    const float anim_speed = GetAnimSpeed();
    bool anyHovered = false;

    // Check if any color button is hovered
    for (size_t i = 0; i < colorStates.size(); i++) {
        float buttonX = total_bb.Max.x - (total_bb.GetHeight() - colorButtonSize) / 2 - totalColorWidth + i * (colorButtonSize + elementPadding);
        float buttonY = total_bb.Min.y + (total_bb.GetHeight() - colorButtonSize) / 2;
        ImRect buttonRect(ImVec2(buttonX, buttonY), ImVec2(buttonX + colorButtonSize, buttonY + colorButtonSize));

        if (ImGui::IsMouseHoveringRect(buttonRect.Min, buttonRect.Max, true)) {
            anyHovered = true;
            break;
        }
    }

    // Color display animations - match CheckboxComponent styling exactly
    it_anim->second.check_color = ImLerp(it_anim->second.check_color,
        hovered ? gui.checkboxstroke : ImColor(34, 27, 27, 255), anim_speed / 3);
    it_anim->second.check_rect_color = ImLerp(it_anim->second.check_rect_color,
        hovered ? gui.checkboxstrokeactive : gui.checkboxstroke, anim_speed / 3);
    it_anim->second.rect_color = ImLerp(it_anim->second.rect_color,
        hovered ? gui.main : func.GetColorWithAlpha(gui.main, 0.3f), anim_speed);
    it_anim->second.rect_shadow_color = ImLerp(it_anim->second.rect_shadow_color,
        hovered ? gui.main : func.GetColorWithAlpha(ImColor(0, 0, 0, 255), 0.3f), anim_speed);
    it_anim->second.icon_color = ImLerp(it_anim->second.icon_color,
        hovered ? func.GetDarkColor(gui.main) : gui.main, anim_speed);
    it_anim->second.background_color = ImLerp(it_anim->second.background_color,
        hovered ? ImColor(25, 19, 15, 155) : func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f), anim_speed);
    it_anim->second.background_rect_color = ImLerp(it_anim->second.background_rect_color,
        hovered ? gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f), anim_speed);

    // --- Rendering ---
    // Main background with rounded corners
    window->DrawList->AddRectFilled(total_bb.Min, total_bb.Max,
        GetColorU32(it_anim->second.background_color), 3.0f);

    // Main border with rounded corners
    window->DrawList->AddRect(total_bb.Min, total_bb.Max,
        GetColorU32(it_anim->second.background_rect_color), 3.0f);

    // Left background rectangle with shadow - match CheckboxComponent
    GetWindowDrawList()->AddRectFilled(total_bb.Min,
        ImVec2(total_bb.Min.x + total_bb.GetSize().y, total_bb.Min.y + total_bb.GetSize().y),
        GetColorU32(it_anim->second.rect_color), 3.0f, ImDrawFlags_RoundCornersLeft);

    window->DrawList->AddShadowRect(total_bb.Min,
        ImVec2(total_bb.Min.x + total_bb.GetSize().y - 5, total_bb.Max.y),
        GetColorU32(it_anim->second.rect_shadow_color), 20.f, ImVec2(0, 0), 0, ImDrawFlags_RoundCornersLeft);

    // Icon background shadow - match CheckboxComponent
    window->DrawList->AddShadowCircle(
        ImVec2(total_bb.Min.x + total_bb.GetSize().y / 2, total_bb.Min.y + total_bb.GetSize().y / 2),
        9.f, GetColorU32(it_anim->second.icon_color), 40, ImVec2(0, 0), 0, 360);

    // Text labels
    window->DrawList->AddText(label_pos, gui.text[0], name);
    window->DrawList->AddText(ImVec2(label_pos.x, label_pos.y + label_size.y + 2), gui.text[1], description);

    // --- Custom icon rendering for description ---
    if (icon != nullptr) {
        PushFont(iconsBig);
        window->DrawList->AddText(
            func.CalcTextPos(total_bb.Min,
                ImVec2(total_bb.Min.x + total_bb.GetSize().y, total_bb.Min.y + total_bb.GetSize().y),
                icon),
            GetColorU32(it_anim->second.icon_color),
            icon);
        PopFont();
    }

    // --- Color picker popup handling ---
    static std::map<std::string, bool> popupActiveMap;
    static std::map<std::string, int> activeColorIndexMap;

    auto& isPopupActive = popupActiveMap[featureName];
    auto& activeColorIndex = activeColorIndexMap[featureName];

    bool colorChanged = false;
    bool anyColorButtonClicked = false;

    // --- Color buttons rendering and interaction ---
    for (size_t i = 0; i < colorStates.size(); i++) {
        float buttonX = total_bb.Max.x - (total_bb.GetHeight() - colorButtonSize) / 2 - totalColorWidth + i * (colorButtonWidth + elementPadding);
        float buttonY = total_bb.Min.y + (total_bb.GetHeight() - colorButtonSize) / 2;
        ImRect buttonRect(ImVec2(buttonX, buttonY), ImVec2(buttonX + colorButtonWidth, buttonY + colorButtonSize));

        // Convert float array to ImVec4 for color display
        ImVec4 displayColor(colorStates[i].color[0], colorStates[i].color[1],
            colorStates[i].color[2], colorStates[i].color[3]);
        ImU32 colorU32 = ColorConvertFloat4ToU32(displayColor);

        bool buttonHovered = ImGui::IsMouseHoveringRect(buttonRect.Min, buttonRect.Max, true);
        bool buttonPressed = buttonHovered && ImGui::IsMouseClicked(0);

        // Color button background - match checkbox styling exactly
        GetWindowDrawList()->AddRectFilled(buttonRect.Min, buttonRect.Max,
            GetColorU32(it_anim->second.check_color), 30.f);

        // Color button border - match checkbox styling exactly
        GetWindowDrawList()->AddRect(buttonRect.Min, buttonRect.Max,
            GetColorU32(it_anim->second.check_rect_color), 30.f);

        // Color button shadow - match checkbox styling
        window->DrawList->AddShadowCircle(
            ImVec2((buttonRect.Min.x + buttonRect.Max.x) / 2, (buttonRect.Min.y + buttonRect.Max.y) / 2),
            8.f, GetColorU32(it_anim->second.check_color), 15.f, ImVec2(0, 0), 0, 360);

        // Alpha checkerboard for transparency
        if (colorStates[i].color[3] < 1.0f) {
            ImGui::RenderColorRectWithAlphaCheckerboard(window->DrawList,
                buttonRect.Min, buttonRect.Max,
                ImColor(1.0f, 1.0f, 1.0f, 1.0f),
                30.f, ImVec2(3, 3), 6.0f, ImDrawFlags_RoundCornersAll);
        }

        // Color fill - slightly inset to show border
        ImRect colorFillRect(
            ImVec2(buttonRect.Min.x + 2, buttonRect.Min.y + 2),
            ImVec2(buttonRect.Max.x - 2, buttonRect.Max.y - 2)
        );
        GetWindowDrawList()->AddRectFilled(colorFillRect.Min, colorFillRect.Max,
            colorU32, 28.f);

        // Handle button click with debouncing
        if (buttonPressed) {
            // Prevent rapid clicking during popup operations
            static std::map<std::string, float> lastColorButtonClickTime;
            float currentTime = ImGui::GetTime();
            
            if (lastColorButtonClickTime.find(featureName) == lastColorButtonClickTime.end() || 
                (currentTime - lastColorButtonClickTime[featureName]) > 0.2f) { // 200ms debounce
                
                activeColorIndex = (int)i;
                isPopupActive = true;
                gui.picker_active = true;
                gui.darkoverlay = true;
                state.active = true;
                anyColorButtonClicked = true;
                
                // Ensure dark overlay stays active for minimum time
                static std::map<std::string, float> darkOverlayStartTime;
                darkOverlayStartTime[featureName] = currentTime;
                
                ImGui::OpenPopup(("##ColorPickerPopup_" + featureName).c_str());
                
                lastColorButtonClickTime[featureName] = currentTime;
            }
        }

        // Button tooltip
        if (buttonHovered && !ImGui::IsMouseClicked(0) && !gui.picker_active &&
            !gui.darkoverlay && !isPopupActive) {
            ImGui::BeginTooltip();
            if (!colorStates[i].label.empty()) {
                ImGui::Text("%s", colorStates[i].label.c_str());
            }
            ImGui::Text("R: %.0f G: %.0f B: %.0f A: %.0f",
                colorStates[i].color[0] * 255, colorStates[i].color[1] * 255,
                colorStates[i].color[2] * 255, colorStates[i].color[3] * 255);
            ImGui::EndTooltip();
        }
    }

    // --- Popup styling ---
    ImGui::PushStyleColor(ImGuiCol_WindowBg, func.ImColorToImVec4(gui.window_bg));
    ImGui::PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.window_bg));
    ImGui::PushStyleColor(ImGuiCol_Border, func.ImColorToImVec4(gui.stroke));
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, style.FrameRounding);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 1.f);

    // Center popup
    ImVec2 center(gui.window_pos.x + gui.window_size.x * 0.5f, gui.window_pos.y + gui.window_size.y * 0.5f);
    SetNextWindowPos(center, ImGuiCond_Always, ImVec2(0.5f, 0.5f));
    std::string uniquePopupId = "##ColorPickerPopup_" + featureName;

    // Color picker popup
    if (ImGui::BeginPopup(uniquePopupId.c_str(), ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoTitleBar)) {
        state.hovered = ImGui::IsWindowHovered(ImGuiHoveredFlags_AllowWhenBlockedByPopup |
            ImGuiHoveredFlags_AllowWhenBlockedByActiveItem |
            ImGuiHoveredFlags_ChildWindows);

        // Close popup when clicking outside
        if (ImGui::IsMouseClicked(ImGuiMouseButton_Left) &&
            !ImGui::IsWindowHovered(ImGuiHoveredFlags_AnyWindow | ImGuiHoveredFlags_AllowWhenBlockedByPopup) &&
            !ImGui::IsAnyItemHovered()) {
            isPopupActive = false;
            gui.picker_active = false;
            gui.darkoverlay = false;
            state.active = false;
            activeColorIndex = -1; // Reset active color index
            ImGui::CloseCurrentPopup();
        }

        // Close on Escape key
        if (ImGui::IsKeyPressed(ImGuiKey_Escape)) {
            isPopupActive = false;
            gui.picker_active = false;
            gui.darkoverlay = false;
            state.active = false;
            activeColorIndex = -1; // Reset active color index
            ImGui::CloseCurrentPopup();
        }

        state.active = true;

        // Color picker (only if we have a valid active color index)
        if (activeColorIndex >= 0 && activeColorIndex < (int)colorStates.size()) {
            // Work with a local copy that gets updated in real-time
            static float tempColor[4] = { 0, 0, 0, 1 };
            static bool tempColorInitialized = false;

            // Initialize temp color when popup opens
            if (!tempColorInitialized || anyColorButtonClicked) {
                tempColor[0] = colorStates[activeColorIndex].color[0];
                tempColor[1] = colorStates[activeColorIndex].color[1];
                tempColor[2] = colorStates[activeColorIndex].color[2];
                tempColor[3] = colorStates[activeColorIndex].color[3];
                tempColorInitialized = true;
            }

            ImGuiColorEditFlags flags = ImGuiColorEditFlags_DisplayRGB |
                ImGuiColorEditFlags_AlphaBar |
                ImGuiColorEditFlags_NoSidePreview;

            ImGui::SetNextItemWidth(18.f * 11.5f);
            bool pickerChanged = nav_elements::ColorPicker4("##colorpicker", tempColor, flags, nullptr);

            // Update the actual color state immediately when picker changes
            if (pickerChanged || ImGui::IsItemActive()) {
                colorStates[activeColorIndex].color[0] = tempColor[0];
                colorStates[activeColorIndex].color[1] = tempColor[1];
                colorStates[activeColorIndex].color[2] = tempColor[2];
                colorStates[activeColorIndex].color[3] = tempColor[3];

                // Update original color pointer if available
                if (colorStates[activeColorIndex].originalColor != nullptr) {
                    colorStates[activeColorIndex].originalColor[0] = tempColor[0];
                    colorStates[activeColorIndex].originalColor[1] = tempColor[1];
                    colorStates[activeColorIndex].originalColor[2] = tempColor[2];
                    colorStates[activeColorIndex].originalColor[3] = tempColor[3];
                }

                colorChanged = true;
                ImGui::MarkItemEdited(ImGui::GetCurrentContext()->LastItemData.ID);
                ImGui::MarkItemEdited(id);
            }
        }

        ImGui::EndPopup();
    }
    else {
        // Reset temp color initialization when popup closes
        static bool tempColorInitialized = false;
        tempColorInitialized = false;
        
        // Reset state when popup is closed externally (with delay to prevent flickering)
        static float lastPopupCloseTime = 0.0f;
        static std::map<std::string, float> darkOverlayStartTime;
        
        if (isPopupActive) {
            lastPopupCloseTime = ImGui::GetTime();
            // Don't immediately reset - wait a bit to see if popup actually closed
        } else if (gui.darkoverlay && (ImGui::GetTime() - lastPopupCloseTime) > 0.1f) {
            // Only reset after a delay to prevent flickering during popup opening
            // Also ensure minimum time has passed since dark overlay was activated
            float timeSinceActivation = ImGui::GetTime() - darkOverlayStartTime[featureName];
            if (timeSinceActivation > 0.3f) { // Minimum 300ms before allowing close
                gui.picker_active = false;
                gui.darkoverlay = false;
                state.active = false;
                activeColorIndex = -1;
            }
        }
    }

    ImGui::PopStyleColor(3);
    ImGui::PopStyleVar(3);

    return colorChanged;
}

bool nav_elements::FontSelector(const char* label, std::string* selectedFont, const char* description, const char* icon) {
    // Get available fonts from the font system
    const auto& availableFonts = g_FontSystem.GetAvailableFonts();
    
    if (availableFonts.empty()) {
        return false;
    }
    
    // Create font names array for combo
    std::vector<const char*> fontNames;
    std::vector<std::string> fontNameStrings;
    
    for (const auto& font : availableFonts) {
        fontNameStrings.push_back(font.name);
        fontNames.push_back(fontNameStrings.back().c_str());
    }
    
    // Find current selection index
    int currentIndex = 0;
    for (size_t i = 0; i < availableFonts.size(); ++i) {
        if (availableFonts[i].name == *selectedFont) {
            currentIndex = static_cast<int>(i);
            break;
        }
    }
    
    // Use existing ComboEx component
    bool changed = ComboEx(label, &currentIndex, fontNames.data(), static_cast<int>(fontNames.size()), description,icon );
    
    if (changed && currentIndex >= 0 && currentIndex < static_cast<int>(availableFonts.size())) {
        *selectedFont = availableFonts[currentIndex].name;
        return true;
    }
    
    return false;
}
