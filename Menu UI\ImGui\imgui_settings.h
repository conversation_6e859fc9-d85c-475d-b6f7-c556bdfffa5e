#pragma once

#define IMGUI_DEFINE_MATH_OPERATORS

#include "imgui.h"
#include "imgui_impl_win32.h"
#include "imgui_impl_dx11.h"
#include "imgui_internal.h"

#include <vector>
#include <string>
#include <map>

#include <cmath>
#include <cstdint>
#include <sstream>
#include <iomanip>
#include "../../Menu UI/Framwork/GUI.h"

inline ImColor hexToImColor(const std::string& hex)
{
    std::stringstream ss;
    ss << std::hex << hex;

    uint32_t colorInt = 0;
    ss >> colorInt;

    float r = ((colorInt >> 16) & 0xFF) / 255.0f;
    float g = ((colorInt >> 8) & 0xFF) / 255.0f;
    float b = ((colorInt) & 0xFF) / 255.0f;

    return ImColor(r, g, b);
}

inline std::vector<float> font_sizes = { 11.0f, 13.0f, 15.0f, 17.0f, 20.0f, 22.0f, 24.0f, 26.0f, 28.0f, 30.0f };
inline std::vector<std::string> font_types = { "semibold", "medium", "bold", "regular", "icon"};
inline std::map<std::string, std::vector<ImFont*>> fonts;

inline static ImDrawList* menu_fore_draw;

inline bool tab_changed;
inline bool tab_changing;

inline bool main_color_changed;
inline bool rainbow_color;
inline int rainbow_time;
inline ImColor main_changed_color;

inline ImColor ImLerpCol(const ImColor& col_a, const ImColor& col_b, float t) {
    ImVec4 lerped_color = ImLerp((ImVec4)col_a, (ImVec4)col_b, t);
    return ImColor(lerped_color);
}

class InterfaceStyling
{
private:

    ImColor GetSmoothColor(float t) {
        float r = (sin(t * 0.1f) + 1.0f) * 0.5f;
        float g = (sin(t * 0.1f + 2.0f) + 1.0f) * 0.5f;
        float b = (sin(t * 0.1f + 4.0f) + 1.0f) * 0.5f;
        return ImColor(r, g, b);
    }

public:

    bool bDarkTheme = true;
    ImColor title;
    ImColor background;
    ImColor main = ImColor(accent_color[2], accent_color[1], accent_color[0], accent_color[3]);
    ImColor main_for_border = ImColor(accent_color[2], accent_color[1], accent_color[0], 0.20f);
    ImColor text[2];
    ImColor window_bg;
    ImColor second;
    ImColor fourth;
    ImColor third;
    ImColor stroke;
    ImColor checkboxbackground;
    ImColor checkboxstroke;
    ImColor checkboxstrokeactive;
    ImVec2 window_padding;
    ImGuiWindow* picker_window;
    ImGuiWindow* picker_col_window;
    ImGuiWindow* combo_window;
    ImGuiWindow* keybind_window;
    ImGuiWindow* main_window;
    ImVec2 window_pos, window_size;
    bool menu_disabled, picker_active = false, darkoverlay = false;
    float item_rounding;
	float combo_rotation;


    InterfaceStyling() {
        ImGuiContext& g = *GImGui;
        title = ImColor(13, 13, 15, 125);
        window_bg = ImColor(25, 22, 22, 250);
        third = ImColor(26, 26, 29, 178);
        fourth = ImColor(22, 22, 25, 255);
        background = ImColor(22, 22, 25, 250);
        checkboxstroke = ImColor(44, 37, 37);
        checkboxstrokeactive = ImColor(44, 37, 37);
        checkboxbackground = ImColor(44, 37, 37, 250);

        text[0] = ImColor(1.f, 1.f, 1.0f, 1.0f);
        text[1] = ImColor(1.f, 1.f, 1.f, 0.7f);
        second = ImColor(17, 17, 20, 255);
        stroke = ImColor(255, 255, 255, 10);
        window_padding = ImVec2(0, 0);
        item_rounding = 4.f;
        menu_disabled = false;
    }
    class utils
    {
    public:
        inline ImVec4 ImColorToImVec4(const ImColor& color) { return ImVec4(color.Value.x, color.Value.y, color.Value.z, color.Value.w); }

        static ImVec2 CalcTextPos(ImVec2 min, ImVec2 max, const char* text, ImFont* font = nullptr) {
            if(font != nullptr) ImGui::PushFont(font);
            ImVec2 pos = min + (max - min) / 2 - ImGui::CalcTextSize(text) / 2;
            if (font != nullptr) ImGui::PopFont();
            return pos;
        }
        static ImColor GetColorWithAlpha(const ImColor& color, float alpha) {
            return ImColor(color.Value.x, color.Value.y, color.Value.z, alpha);
        }

        static ImColor GetAdjustedColor(const ImVec4& color, float adjustment)
        {
            return ImVec4(color.x * adjustment, color.y * adjustment, color.z * adjustment, 1.0f);
        }

        // It's better to create a method for both light and dark modifications since the only difference is the scale
        static ImColor GetDarkColor(const ImColor& color) { return GetAdjustedColor(color, 0.35f); }
        static ImColor GetLightColor(const ImVec4& color) { return GetAdjustedColor(color, 1.65f); }

    };
};

inline std::vector<std::string> stringToVector(std::string str, std::string token)
{
    std::vector<std::string> result;
    while (str.size())
    {
        int index = str.find(token);
        if (index != std::string::npos)
        {
            result.push_back(str.substr(0, index));
            str = str.substr(index + token.size());
            if (str.size() == 0)
                result.push_back(str);
        }
        else
        {
            result.push_back(str);
            str = "";
        }
    }
    return result;
}

constexpr int ypadding = 3; // vertical spacing of text in pixels

inline void DrawCenteredText(ImVec2 position, ImVec2 boundry, ImVec2 charSize, int idx, std::string Text)
{
    ImVec2 renderPos;
    ImVec2 linesize = ImGui::CalcTextSize(Text.c_str());
    renderPos.x = position.x + ((boundry.x / 2) - ((linesize.x + charSize.x) / 2)); // horizontal position centered in boundry x
    renderPos.y = position.y + ((idx * linesize.y) + ypadding);
    ImGui::RenderText(renderPos, Text.c_str(), 0, false);
}

inline void CenterText(std::string text, ImVec2 position, int boundry_width, int lines = 3)
{
    ImVec2 charsize = ImGui::CalcTextSize("X"); // TODO: should do per font, global constexpr
    std::vector<std::string> words = stringToVector(text, " ");

    int linecnt = 0; // 0 is first line
    int wordcnt = 0; // 0 is first word / vector item
    int linex = 0; // space already used on line
    std::string lineText = "";

    for (auto& word : words)
    {
        wordcnt++;
        int wordsize = (word.length() + 1) * charsize.x; // size of this word in pixels, plus one space

        if ((linex + wordsize < boundry_width)) // if word will fit in space available
        {
            lineText += word + " "; // add it
            linex += wordsize;      // reduce the available space
            if (wordcnt == words.size()) // last one
            {
                DrawCenteredText(position, ImVec2(boundry_width, boundry_width), charsize, linecnt, lineText.c_str());
                continue;
            }
        }

        else // word won't fit on this line, so draw current line, then start next line...
        {
            DrawCenteredText(position, ImVec2(boundry_width, boundry_width), charsize, linecnt, lineText.c_str());
            // clear linex and lineText, increment linecnt
            lineText = word + " ";
            linex = 0;
            linecnt++;
            continue;
        }

    }
}

//custom imgui functiouns

namespace ImGui
{
    inline void AddTextWithFont(const char* text, ImFont* font, ImColor color = ImColor(1.f, 1.f, 1.f, 1.f))
    {

        InterfaceStyling::utils func;
        ImGui::PushStyleColor(ImGuiCol_Text, func.ImColorToImVec4(color));
        ImGui::PushFont(font);
        ImGui::Text(text);
        ImGui::PopFont();
        ImGui::PopStyleColor();
    }

    inline void ShadeVertsLinearColorGradientSetAlpha(ImDrawList* draw_list, int vert_start_idx, int vert_end_idx, ImVec2 gradient_p0, ImVec2 gradient_p1, ImU32 col0, ImU32 col1)
    {
        ImVec2 gradient_extent = gradient_p1 - gradient_p0;
        float gradient_inv_length2 = 1.0f / ImLengthSqr(gradient_extent);
        ImDrawVert* vert_start = draw_list->VtxBuffer.Data + vert_start_idx;
        ImDrawVert* vert_end = draw_list->VtxBuffer.Data + vert_end_idx;
        const int col0_r = (int)(col0 >> IM_COL32_R_SHIFT) & 0xFF;
        const int col0_g = (int)(col0 >> IM_COL32_G_SHIFT) & 0xFF;
        const int col0_b = (int)(col0 >> IM_COL32_B_SHIFT) & 0xFF;
        const int col0_a = (int)(col0 >> IM_COL32_A_SHIFT) & 0xFF;
        const int col_delta_r = ((int)(col1 >> IM_COL32_R_SHIFT) & 0xFF) - col0_r;
        const int col_delta_g = ((int)(col1 >> IM_COL32_G_SHIFT) & 0xFF) - col0_g;
        const int col_delta_b = ((int)(col1 >> IM_COL32_B_SHIFT) & 0xFF) - col0_b;
        const int col_delta_a = ((int)(col1 >> IM_COL32_A_SHIFT) & 0xFF) - col0_a;
        for (ImDrawVert* vert = vert_start; vert < vert_end; vert++)
        {
            float d = ImDot(vert->pos - gradient_p0, gradient_extent);
            float t = ImClamp(d * gradient_inv_length2, 0.0f, 1.0f);
            int r = (int)(col0_r + col_delta_r * t);
            int g = (int)(col0_g + col_delta_g * t);
            int b = (int)(col0_b + col_delta_b * t);
            int a = (int)(col0_a + col_delta_a * t);
            vert->col = (r << IM_COL32_R_SHIFT) | (g << IM_COL32_G_SHIFT) | (b << IM_COL32_B_SHIFT) | (a << IM_COL32_A_SHIFT);
        }
    }

    struct shader_state
    {
        int vtx_idx_1;
        int vtx_idx_2;
    };

    inline void BeginShader(const char* label)
    {
        ImGuiWindow* window = GetCurrentWindow();
        if (window->SkipItems)
            return;

        ImGuiContext& g = *GImGui;
        const ImGuiID id = window->GetID(label);

        static std::map<ImGuiID, shader_state> anim;
        auto it_anim = anim.find(id);

        if (it_anim == anim.end())
        {
            anim.insert({ id, shader_state() });
            it_anim = anim.find(id);
        }

        it_anim->second.vtx_idx_1 = GetWindowDrawList()->VtxBuffer.Size;
    }


    inline void EndShader(const char* label, ImVec2 pos, ImVec2 size, ImColor color1, ImColor color2)
    {
        ImGuiWindow* window = GetCurrentWindow();
        if (window->SkipItems)
            return;

        ImGuiContext& g = *GImGui;
        const ImGuiID id = window->GetID(label);

        static std::map<ImGuiID, shader_state> anim;
        auto it_anim = anim.find(id);

        if (it_anim == anim.end())
        {
            anim.insert({ id, shader_state() });
            it_anim = anim.find(id);
        }

        it_anim->second.vtx_idx_2 = GetWindowDrawList()->VtxBuffer.Size;
        ShadeVertsLinearColorGradientSetAlpha(ImGui::GetWindowDrawList(), it_anim->second.vtx_idx_1, it_anim->second.vtx_idx_2, pos, pos + size, color1, color2);
    }
}

inline InterfaceStyling gui;
inline InterfaceStyling::utils func;
